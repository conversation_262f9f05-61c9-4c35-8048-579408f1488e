import { createContext } from 'react';
import { Session, User } from '@supabase/supabase-js';

// Define the shape of the context data (export it)
export interface AuthContextType {
  session: Session | null;
  user: User | null;
  isAuthenticated: boolean; // Derived from session
  isLoading: boolean;
  login: (email: string, password: string) => Promise<{ error: Error | null }>;
  logout: () => Promise<{ error: Error | null }>;
  getToken: () => Promise<string | null>;
}

// Create the context with a default value (export it)
// The default value here doesn't matter much as the hook checks for undefined
export const AuthContext = createContext<AuthContextType | undefined>(undefined);

// AuthProvider component is now in src/providers/AuthProvider.tsx
// useAuth hook is now in src/hooks/useAuth.ts