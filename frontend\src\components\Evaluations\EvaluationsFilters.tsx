import React from 'react';
import {
  Filter,
  X,
  Calendar,
  BarChart3,
  RefreshCw
} from 'lucide-react';
import { Button } from '../UI/button';
import { Input } from '../UI/input';
import { Label } from '../UI/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '../UI/select';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '../UI/popover';
import { Badge } from '../UI/badge';
import { Separator } from '../UI/separator';
import {
  EstadoEvaluacion,
  EvaluacionSortBy,
  EvaluacionGroupBy
} from '../../types/evaluaciones';

interface EvaluationsFiltersProps {
  filters: {
    estado: EstadoEvaluacion[];
    agente_id: string[];
    puntuacion_min?: number;
    puntuacion_max?: number;
    fecha_desde?: string;
    fecha_hasta?: string;
    sort_by: EvaluacionSortBy;
    group_by: EvaluacionGroupBy;
  };
  onFiltersChange: (filters: any) => void;
  onReset: () => void;
}

const estadoOptions = [
  { value: EstadoEvaluacion.PENDIENTE_REVISION, label: 'Pendiente Revisión' },
  { value: EstadoEvaluacion.REVISADO, label: 'Revisado' },
  { value: EstadoEvaluacion.PENDIENTE_MEJORAS, label: 'Pendiente Mejoras' },
  { value: EstadoEvaluacion.MEJORAS_APLICADAS, label: 'Mejoras Aplicadas' },
];

const sortOptions = [
  { value: EvaluacionSortBy.FECHA_DESC, label: 'Fecha (Más reciente)' },
  { value: EvaluacionSortBy.FECHA_ASC, label: 'Fecha (Más antigua)' },
  { value: EvaluacionSortBy.PUNTUACION_DESC, label: 'Puntuación (Mayor)' },
  { value: EvaluacionSortBy.PUNTUACION_ASC, label: 'Puntuación (Menor)' },
  { value: EvaluacionSortBy.AGENTE_ASC, label: 'Agente (A-Z)' },
  { value: EvaluacionSortBy.AGENTE_DESC, label: 'Agente (Z-A)' },
];

const groupOptions = [
  { value: EvaluacionGroupBy.NINGUNO, label: 'Sin agrupar' },
  { value: EvaluacionGroupBy.ESTADO, label: 'Por Estado' },
  { value: EvaluacionGroupBy.AGENTE, label: 'Por Agente' },
  { value: EvaluacionGroupBy.WORKFLOW, label: 'Por Workflow' },
];

export const EvaluationsFilters: React.FC<EvaluationsFiltersProps> = ({
  filters,
  onFiltersChange,
  onReset,
}) => {


  const activeFiltersCount = [
    filters.estado.length > 0,
    filters.agente_id.length > 0,
    filters.puntuacion_min !== undefined,
    filters.puntuacion_max !== undefined,
    filters.fecha_desde !== undefined,
    filters.fecha_hasta !== undefined,
  ].filter(Boolean).length;

  const handleEstadoChange = (estado: EstadoEvaluacion, checked: boolean) => {
    const newEstados = checked
      ? [...filters.estado, estado]
      : filters.estado.filter(e => e !== estado);
    
    onFiltersChange({ estado: newEstados });
  };

  const handlePuntuacionChange = (field: 'puntuacion_min' | 'puntuacion_max', value: string) => {
    const numValue = value === '' ? undefined : parseFloat(value);
    onFiltersChange({ [field]: numValue });
  };

  const handleFechaChange = (field: 'fecha_desde' | 'fecha_hasta', value: string) => {
    onFiltersChange({ [field]: value || undefined });
  };

  return (
    <div className="flex items-center gap-2 flex-wrap">
      {/* Quick Sort */}
      <Select
        value={filters.sort_by}
        onValueChange={(value) => onFiltersChange({ sort_by: value })}
      >
        <SelectTrigger className="w-48">
          <BarChart3 className="h-4 w-4 mr-2" />
          <SelectValue />
        </SelectTrigger>
        <SelectContent>
          {sortOptions.map(option => (
            <SelectItem key={option.value} value={option.value}>
              {option.label}
            </SelectItem>
          ))}
        </SelectContent>
      </Select>

      {/* Group By */}
      <Select
        value={filters.group_by}
        onValueChange={(value) => onFiltersChange({ group_by: value })}
      >
        <SelectTrigger className="w-40">
          <SelectValue />
        </SelectTrigger>
        <SelectContent>
          {groupOptions.map(option => (
            <SelectItem key={option.value} value={option.value}>
              {option.label}
            </SelectItem>
          ))}
        </SelectContent>
      </Select>

      {/* Advanced Filters */}
      <Popover>
        <PopoverTrigger asChild>
          <Button variant="outline" className="relative">
            <Filter className="h-4 w-4 mr-2" />
            Filtros
            {activeFiltersCount > 0 && (
              <Badge 
                variant="secondary" 
                className="ml-2 h-5 w-5 p-0 flex items-center justify-center text-xs"
              >
                {activeFiltersCount}
              </Badge>
            )}
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-80" align="start">
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <h4 className="font-medium">Filtros Avanzados</h4>
              <Button
                variant="ghost"
                size="sm"
                onClick={onReset}
                className="h-8 px-2"
              >
                <RefreshCw className="h-4 w-4 mr-1" />
                Limpiar
              </Button>
            </div>

            <Separator />

            {/* Estado Filter */}
            <div className="space-y-2">
              <Label className="text-sm font-medium">Estado</Label>
              <div className="space-y-2">
                {estadoOptions.map(option => (
                  <div key={option.value} className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      id={`estado-${option.value}`}
                      checked={filters.estado.includes(option.value)}
                      onChange={(e) => handleEstadoChange(option.value, e.target.checked)}
                      className="rounded border-gray-300"
                    />
                    <Label 
                      htmlFor={`estado-${option.value}`}
                      className="text-sm cursor-pointer"
                    >
                      {option.label}
                    </Label>
                  </div>
                ))}
              </div>
            </div>

            <Separator />

            {/* Puntuación Range */}
            <div className="space-y-2">
              <Label className="text-sm font-medium">Rango de Puntuación</Label>
              <div className="flex items-center space-x-2">
                <Input
                  type="number"
                  placeholder="Min"
                  min="0"
                  max="10"
                  step="0.1"
                  value={filters.puntuacion_min || ''}
                  onChange={(e) => handlePuntuacionChange('puntuacion_min', e.target.value)}
                  className="w-20"
                />
                <span className="text-gray-500">-</span>
                <Input
                  type="number"
                  placeholder="Max"
                  min="0"
                  max="10"
                  step="0.1"
                  value={filters.puntuacion_max || ''}
                  onChange={(e) => handlePuntuacionChange('puntuacion_max', e.target.value)}
                  className="w-20"
                />
              </div>
            </div>

            <Separator />

            {/* Fecha Range */}
            <div className="space-y-2">
              <Label className="text-sm font-medium flex items-center">
                <Calendar className="h-4 w-4 mr-1" />
                Rango de Fechas
              </Label>
              <div className="space-y-2">
                <div>
                  <Label htmlFor="fecha-desde" className="text-xs text-gray-500">
                    Desde
                  </Label>
                  <Input
                    id="fecha-desde"
                    type="date"
                    value={filters.fecha_desde || ''}
                    onChange={(e) => handleFechaChange('fecha_desde', e.target.value)}
                  />
                </div>
                <div>
                  <Label htmlFor="fecha-hasta" className="text-xs text-gray-500">
                    Hasta
                  </Label>
                  <Input
                    id="fecha-hasta"
                    type="date"
                    value={filters.fecha_hasta || ''}
                    onChange={(e) => handleFechaChange('fecha_hasta', e.target.value)}
                  />
                </div>
              </div>
            </div>
          </div>
        </PopoverContent>
      </Popover>

      {/* Active Filters Display */}
      {activeFiltersCount > 0 && (
        <div className="flex items-center gap-1 flex-wrap">
          {filters.estado.map(estado => (
            <Badge key={estado} variant="secondary" className="text-xs">
              {estadoOptions.find(o => o.value === estado)?.label}
              <button
                onClick={() => handleEstadoChange(estado, false)}
                className="ml-1 hover:bg-gray-300 rounded-full p-0.5"
              >
                <X className="h-3 w-3" />
              </button>
            </Badge>
          ))}
          
          {(filters.puntuacion_min !== undefined || filters.puntuacion_max !== undefined) && (
            <Badge variant="secondary" className="text-xs">
              Puntuación: {filters.puntuacion_min || 0}-{filters.puntuacion_max || 10}
              <button
                onClick={() => onFiltersChange({ puntuacion_min: undefined, puntuacion_max: undefined })}
                className="ml-1 hover:bg-gray-300 rounded-full p-0.5"
              >
                <X className="h-3 w-3" />
              </button>
            </Badge>
          )}
          
          {(filters.fecha_desde || filters.fecha_hasta) && (
            <Badge variant="secondary" className="text-xs">
              Fechas
              <button
                onClick={() => onFiltersChange({ fecha_desde: undefined, fecha_hasta: undefined })}
                className="ml-1 hover:bg-gray-300 rounded-full p-0.5"
              >
                <X className="h-3 w-3" />
              </button>
            </Badge>
          )}
        </div>
      )}
    </div>
  );
};
