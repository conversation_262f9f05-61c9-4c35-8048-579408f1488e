import React, { useMemo, useRef, useEffect } from 'react';
import { FixedSizeList as List } from 'react-window';
import { ChatMessage } from '../../contexts/ChatContext';
import ChatMessageComponent from './ChatMessage';

interface VirtualizedChatHistoryProps {
  messages: ChatMessage[];
  isProcessingMessage: boolean;
  className?: string;
}

// Altura estimada por mensaje (en píxeles)
const ITEM_HEIGHT = 120;
// Altura máxima del contenedor de chat
const MAX_CONTAINER_HEIGHT = 600;

interface MessageItemProps {
  index: number;
  style: React.CSSProperties;
  data: {
    messages: ChatMessage[];
    isProcessingMessage: boolean;
  };
}

const MessageItem: React.FC<MessageItemProps> = ({ index, style, data }) => {
  const { messages, isProcessingMessage } = data;
  const message = messages[index];

  if (!message) {
    return <div style={style} />;
  }

  return (
    <div style={style}>
      <ChatMessageComponent
        message={message}
        isProcessingMessage={isProcessingMessage && index === messages.length - 1}
      />
    </div>
  );
};

const VirtualizedChatHistory: React.FC<VirtualizedChatHistoryProps> = ({
  messages,
  isProcessingMessage,
  className = '',
}) => {
  const listRef = useRef<List>(null);

  // Calcular altura del contenedor basada en el número de mensajes
  const containerHeight = useMemo(() => {
    const totalHeight = messages.length * ITEM_HEIGHT;
    return Math.min(totalHeight, MAX_CONTAINER_HEIGHT);
  }, [messages.length]);

  // Auto-scroll al último mensaje cuando se añaden nuevos mensajes
  useEffect(() => {
    if (listRef.current && messages.length > 0) {
      listRef.current.scrollToItem(messages.length - 1, 'end');
    }
  }, [messages.length]);

  // Si hay pocos mensajes, renderizar normalmente sin virtualización
  if (messages.length <= 10) {
    return (
      <div className={`space-y-4 ${className}`}>
        {messages.map((message, index) => (
          <ChatMessageComponent
            key={message.id || `${message.thread_id}-${index}`}
            message={message}
            isProcessingMessage={isProcessingMessage && index === messages.length - 1}
          />
        ))}
      </div>
    );
  }

  // Para chats largos, usar virtualización
  return (
    <div className={className}>
      <List
        ref={listRef}
        height={containerHeight}
        itemCount={messages.length}
        itemSize={ITEM_HEIGHT}
        itemData={{
          messages,
          isProcessingMessage,
        }}
        overscanCount={5} // Renderizar 5 elementos extra arriba y abajo para scroll suave
      >
        {MessageItem}
      </List>
    </div>
  );
};

export default VirtualizedChatHistory;
