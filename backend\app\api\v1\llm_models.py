from fastapi import APIRouter, Depends, HTTPException, status
from typing import List
from app.core.security import get_current_user_id
from app.services import llm_model_service
from app.models.llm_model import LLMModel
import logging

router = APIRouter()
logger = logging.getLogger(__name__)

@router.get("", response_model=List[LLMModel])
async def read_llm_models(
    current_user_id: str = Depends(get_current_user_id)
):
    """
    Retrieve a list of active LLM models.
    Requires authentication.
    """
    try:
        models = await llm_model_service.get_active_llm_models()
        return models
    except HTTPException as http_exc:
        raise http_exc
    except Exception as e:
        logger.exception(f"Error fetching LLM models: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="An error occurred while fetching LLM models."
        )