import logging
from fastapi import APIRouter, Depends, HTTPException, status, Query
from typing import Optional, List
from uuid import UUID

from app.models.proceso_cliente import (
    ProcesoClienteCreate,
    ProcesoClienteUpdate,
    ProcesoClienteListResponse,
    ProcesoClienteDetalle,
    ProcesoClienteFilters,
    ProcesoClienteGroupBy,
    ValorNegocioCliente,
    ComplejidadAutomatizacion,
    PrioridadAutomatizacion
)
from app.models.tarea_cliente import (
    TareaClienteCreate,
    TareaClienteUpdate,
    TareaClienteListResponse,
    TareaClienteDetalle,
    TareaClienteFilters,
    ProcesoClienteContadores
)
from app.core.security import get_current_user_id
from app.services import proceso_cliente_service, tarea_cliente_service

logger = logging.getLogger(__name__)
router = APIRouter()


@router.get("/empresas/{empresa_id}/procesos_clientes_con_metricas", response_model=ProcesoClienteListResponse)
@router.get("/empresas/{empresa_id}/procesos_clientes_con_metricas/", response_model=ProcesoClienteListResponse)
async def get_procesos_clientes_con_metricas(
    empresa_id: UUID,
    skip: int = Query(0, ge=0, description="Number of records to skip"),
    limit: int = Query(100, ge=1, le=1000, description="Number of records to return"),
    # Filter parameters
    responsable_ids: Optional[str] = Query(None, description="Comma-separated list of responsible person IDs"),
    es_cuello_botella: Optional[bool] = Query(None, description="Filter by bottleneck status"),
    valor_negocio_cliente: Optional[ValorNegocioCliente] = Query(None, description="Filter by business value"),
    es_manual: Optional[bool] = Query(None, description="Filter by manual status"),
    es_repetitivo: Optional[bool] = Query(None, description="Filter by repetitive status"),
    complejidad_automatizacion_aceleralia: Optional[ComplejidadAutomatizacion] = Query(None, description="Filter by automation complexity"),
    prioridad_automatizacion_aceleralia: Optional[PrioridadAutomatizacion] = Query(None, description="Filter by automation priority"),
    search: Optional[str] = Query(None, description="Search in name and description"),
    group_by: Optional[ProcesoClienteGroupBy] = Query(None, description="Group results by field"),
    current_user_id: UUID = Depends(get_current_user_id)
):
    """
    Get list of client processes with metrics and filtering.
    """
    try:
        # Parse responsable_ids if provided
        responsable_ids_list = None
        if responsable_ids:
            try:
                responsable_ids_list = [UUID(id.strip()) for id in responsable_ids.split(',') if id.strip()]
            except ValueError:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Invalid responsable_ids format. Must be comma-separated UUIDs."
                )
        
        # Create filters object
        filters = ProcesoClienteFilters(
            responsable_ids=responsable_ids_list,
            es_cuello_botella=es_cuello_botella,
            valor_negocio_cliente=valor_negocio_cliente,
            es_manual=es_manual,
            es_repetitivo=es_repetitivo,
            complejidad_automatizacion_aceleralia=complejidad_automatizacion_aceleralia,
            prioridad_automatizacion_aceleralia=prioridad_automatizacion_aceleralia,
            search=search
        )
        
        return await proceso_cliente_service.get_procesos_clientes_con_metricas(
            empresa_id=empresa_id,
            filters=filters,
            group_by=group_by,
            skip=skip,
            limit=limit
        )
        
    except Exception as e:
        logger.error(f"Error getting client processes: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error retrieving client processes: {str(e)}"
        )


@router.get("/procesos_clientes/{proceso_id}/detalles_completos", response_model=ProcesoClienteDetalle)
@router.get("/procesos_clientes/{proceso_id}/detalles_completos/", response_model=ProcesoClienteDetalle)
async def get_proceso_cliente_detalles_completos(
    proceso_id: UUID,
    current_user_id: UUID = Depends(get_current_user_id)
):
    """
    Get complete details for a specific client process including tasks.
    """
    try:
        proceso = await proceso_cliente_service.get_proceso_cliente_detalle(proceso_id)
        
        if not proceso:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Client process not found"
            )
        
        return proceso
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting process details: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error retrieving process details: {str(e)}"
        )


@router.patch("/procesos_clientes/{proceso_id}", response_model=ProcesoClienteDetalle)
@router.patch("/procesos_clientes/{proceso_id}/", response_model=ProcesoClienteDetalle)
async def update_proceso_cliente(
    proceso_id: UUID,
    update_data: ProcesoClienteUpdate,
    current_user_id: UUID = Depends(get_current_user_id)
):
    """
    Update a client process (inline editing).
    """
    try:
        proceso = await proceso_cliente_service.update_proceso_cliente(proceso_id, update_data)
        
        if not proceso:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Client process not found"
            )
        
        return proceso
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error updating process: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error updating process: {str(e)}"
        )


@router.get("/procesos_clientes/{proceso_id}/tareas", response_model=TareaClienteListResponse)
@router.get("/procesos_clientes/{proceso_id}/tareas/", response_model=TareaClienteListResponse)
async def get_tareas_cliente_by_proceso(
    proceso_id: UUID,
    skip: int = Query(0, ge=0, description="Number of records to skip"),
    limit: int = Query(100, ge=1, le=1000, description="Number of records to return"),
    # Filter parameters
    responsable_ids: Optional[str] = Query(None, description="Comma-separated list of responsible person IDs"),
    es_manual_cliente: Optional[bool] = Query(None, description="Filter by manual status"),
    search: Optional[str] = Query(None, description="Search in name and description"),
    current_user_id: UUID = Depends(get_current_user_id)
):
    """
    Get list of client tasks for a specific process with filtering.
    """
    try:
        # Parse responsable_ids if provided
        responsable_ids_list = None
        if responsable_ids:
            try:
                responsable_ids_list = [UUID(id.strip()) for id in responsable_ids.split(',') if id.strip()]
            except ValueError:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Invalid responsable_ids format. Must be comma-separated UUIDs."
                )
        
        # Create filters object
        filters = TareaClienteFilters(
            responsable_ids=responsable_ids_list,
            es_manual_cliente=es_manual_cliente,
            search=search
        )
        
        return await tarea_cliente_service.get_tareas_cliente_by_proceso(
            proceso_id=proceso_id,
            filters=filters,
            skip=skip,
            limit=limit
        )
        
    except Exception as e:
        logger.error(f"Error getting client tasks: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error retrieving client tasks: {str(e)}"
        )


@router.patch("/tareas_clientes/{tarea_id}", response_model=TareaClienteDetalle)
@router.patch("/tareas_clientes/{tarea_id}/", response_model=TareaClienteDetalle)
async def update_tarea_cliente(
    tarea_id: UUID,
    update_data: TareaClienteUpdate,
    current_user_id: UUID = Depends(get_current_user_id)
):
    """
    Update a client task (inline editing).
    """
    try:
        tarea = await tarea_cliente_service.update_tarea_cliente(tarea_id, update_data)
        
        if not tarea:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Client task not found"
            )
        
        return tarea
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error updating task: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error updating task: {str(e)}"
        )


@router.get("/tareas_clientes/{tarea_id}", response_model=TareaClienteDetalle)
@router.get("/tareas_clientes/{tarea_id}/", response_model=TareaClienteDetalle)
async def get_tarea_cliente_detalle(
    tarea_id: UUID,
    current_user_id: UUID = Depends(get_current_user_id)
):
    """
    Get detailed information for a specific client task.
    """
    try:
        tarea = await tarea_cliente_service.get_tarea_cliente_detalle(tarea_id)
        
        if not tarea:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Client task not found"
            )
        
        return tarea
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting task details: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error retrieving task details: {str(e)}"
        )


@router.get("/empresas/{empresa_id}/procesos_clientes/contadores", response_model=ProcesoClienteContadores)
@router.get("/empresas/{empresa_id}/procesos_clientes/contadores/", response_model=ProcesoClienteContadores)
async def get_contadores_procesos_cliente(
    empresa_id: UUID,
    current_user_id: UUID = Depends(get_current_user_id)
):
    """
    Get summary counters for client processes.
    """
    try:
        return await proceso_cliente_service.get_contadores_procesos_cliente(empresa_id)
        
    except Exception as e:
        logger.error(f"Error getting process counters: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error retrieving process counters: {str(e)}"
        )


# Utility endpoints for dropdowns and filters
@router.get("/empresas/{empresa_id}/personas_disponibles")
@router.get("/empresas/{empresa_id}/personas_disponibles/")
async def get_personas_disponibles_empresa(
    empresa_id: UUID,
    current_user_id: UUID = Depends(get_current_user_id)
):
    """
    Get list of available persons for the company (for filters).
    """
    try:
        from app.core.database import get_supabase_client
        supabase = await get_supabase_client()
        
        response = supabase.table('personas').select(
            "id, nombre, apellidos, cargo"
        ).eq('empresa_id', str(empresa_id)).eq('activo', True).execute()
        
        return {"personas": response.data or []}
        
    except Exception as e:
        logger.error(f"Error getting available persons: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error retrieving available persons: {str(e)}"
        )


@router.get("/empresas/{empresa_id}/departamentos_disponibles")
@router.get("/empresas/{empresa_id}/departamentos_disponibles/")
async def get_departamentos_disponibles_empresa(
    empresa_id: UUID,
    current_user_id: UUID = Depends(get_current_user_id)
):
    """
    Get list of available departments for the company (for filters).
    """
    try:
        from app.core.database import get_supabase_client
        supabase = await get_supabase_client()
        
        response = supabase.table('departamentos').select(
            "id, nombre, descripcion"
        ).eq('empresa_id', str(empresa_id)).execute()
        
        return {"departamentos": response.data or []}
        
    except Exception as e:
        logger.error(f"Error getting available departments: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error retrieving available departments: {str(e)}"
        )
