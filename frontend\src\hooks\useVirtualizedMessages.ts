import { useState, useEffect, useMemo, useCallback, useRef } from 'react';
import { ChatMessage } from '../contexts/ChatContext';

interface VirtualizationConfig {
  estimatedItemHeight: number;
  bufferSize: number;
  containerHeight: number;
  scrollTop: number;
}

interface VirtualizedResult<T = ChatMessage> {
  visibleMessages: T[];
  totalHeight: number;
  offsetY: number;
  visibleRange: { start: number; end: number };
  isAtBottom: boolean;
}

export const useVirtualizedMessages = <T extends ChatMessage>(
  messages: T[],
  config: VirtualizationConfig
): VirtualizedResult<T> => {
  const { estimatedItemHeight, bufferSize, containerHeight, scrollTop } = config;
  
  // Calcular el rango visible
  const visibleRange = useMemo(() => {
    if (messages.length === 0) return { start: 0, end: 0 };

    const visibleCount = Math.ceil(containerHeight / estimatedItemHeight);
    const startIndex = Math.max(0, Math.floor(scrollTop / estimatedItemHeight) - bufferSize);
    const endIndex = Math.min(messages.length, startIndex + visibleCount + (bufferSize * 2));

    return { start: startIndex, end: endIndex };
  }, [messages.length, containerHeight, scrollTop, estimatedItemHeight, bufferSize]);

  // Mensajes visibles
  const visibleMessages = useMemo(() => {
    return messages.slice(visibleRange.start, visibleRange.end);
  }, [messages, visibleRange]);

  // Altura total
  const totalHeight = useMemo(() => {
    return messages.length * estimatedItemHeight;
  }, [messages.length, estimatedItemHeight]);

  // Offset Y para posicionamiento
  const offsetY = visibleRange.start * estimatedItemHeight;

  // Detectar si está en el bottom (más permisivo para evitar bounce)
  const isAtBottom = useMemo(() => {
    if (containerHeight === 0 || totalHeight === 0) return true;
    // Usar un threshold más grande para evitar el bounce back
    return (totalHeight - scrollTop - containerHeight) <= 100;
  }, [scrollTop, containerHeight, totalHeight]);

  return {
    visibleMessages,
    totalHeight,
    offsetY,
    visibleRange,
    isAtBottom
  };
};

// Hook para manejar el scroll inteligente
export const useSmartScroll = (
  containerRef: React.RefObject<HTMLDivElement | null>,
  messagesLength: number,
  threadId: number | string | null
) => {
  const [scrollTop, setScrollTop] = useState(0);
  const [isAtBottom, setIsAtBottom] = useState(true);
  const [containerHeight, setContainerHeight] = useState(600);
  const [userIsScrolling, setUserIsScrolling] = useState(false);
  const scrollTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Observar cambios de tamaño del contenedor
  useEffect(() => {
    const container = containerRef.current;
    if (!container) return;

    const resizeObserver = new ResizeObserver((entries) => {
      const entry = entries[0];
      if (entry) {
        setContainerHeight(entry.contentRect.height);
      }
    });

    resizeObserver.observe(container);
    return () => resizeObserver.disconnect();
  }, []);

  // Manejar scroll respetando la interacción del usuario
  const handleScroll = useCallback((e: React.UIEvent<HTMLDivElement>) => {
    const target = e.target as HTMLDivElement;
    const { scrollTop: newScrollTop, scrollHeight, clientHeight } = target;

    setScrollTop(newScrollTop);

    // Marcar que el usuario está haciendo scroll
    setUserIsScrolling(true);

    // Limpiar timeout anterior
    if (scrollTimeoutRef.current) {
      clearTimeout(scrollTimeoutRef.current);
    }

    // Después de 150ms sin scroll, considerar que el usuario terminó
    scrollTimeoutRef.current = setTimeout(() => {
      setUserIsScrolling(false);
    }, 150);

    // Detectar si está en el bottom de manera más precisa
    const distanceFromBottom = scrollHeight - newScrollTop - clientHeight;
    const atBottom = distanceFromBottom <= 5; // Threshold muy pequeño

    setIsAtBottom(atBottom);
  }, []);

  // Auto-scroll SOLO si el usuario no está haciendo scroll manualmente
  useEffect(() => {
    // Solo hacer auto-scroll si:
    // 1. Estaba en el bottom antes del nuevo mensaje
    // 2. El usuario NO está haciendo scroll manualmente
    // 3. Hay nuevos mensajes
    if (isAtBottom && !userIsScrolling && containerRef.current && messagesLength > 0) {
      const container = containerRef.current;

      // Usar requestAnimationFrame para sincronizar con el render
      requestAnimationFrame(() => {
        if (container && !userIsScrolling) {
          // Scroll directo al final sin animación para evitar bounce
          container.scrollTop = container.scrollHeight;
        }
      });
    }
  }, [messagesLength, isAtBottom, userIsScrolling]);

  // Scroll al bottom cuando cambia el thread (sin interferir con scroll manual)
  useEffect(() => {
    if (containerRef.current && threadId) {
      const container = containerRef.current;
      // Reset del estado de scroll del usuario
      setUserIsScrolling(false);

      // Scroll inmediato al final para nuevo thread
      setTimeout(() => {
        if (container) {
          container.scrollTop = container.scrollHeight;
          setIsAtBottom(true);
        }
      }, 100);
    }
  }, [threadId]);

  // Función para scroll manual al bottom
  const scrollToBottom = useCallback(() => {
    if (containerRef.current) {
      const container = containerRef.current;
      // Marcar que NO es scroll del usuario para permitir el auto-scroll
      setUserIsScrolling(false);

      container.scrollTo({
        top: container.scrollHeight,
        behavior: 'smooth'
      });
      setIsAtBottom(true);
    }
  }, []);

  // Cleanup del timeout al desmontar
  useEffect(() => {
    return () => {
      if (scrollTimeoutRef.current) {
        clearTimeout(scrollTimeoutRef.current);
      }
    };
  }, []);

  return {
    scrollTop,
    containerHeight,
    isAtBottom,
    handleScroll,
    scrollToBottom,
    userIsScrolling
  };
};

// Tipo extendido para mensajes optimizados
interface OptimizedChatMessage extends ChatMessage {
  stableKey: string;
}

// Hook para optimización de rendimiento
export const useMessageOptimization = (messages: ChatMessage[]) => {
  // Memoizar mensajes para evitar re-renders innecesarios
  const optimizedMessages = useMemo((): OptimizedChatMessage[] => {
    return messages.map(msg => ({
      ...msg,
      // Crear una key estable para React
      stableKey: `${msg.thread_id}-${msg.message_id}-${msg.created_at}`
    }));
  }, [messages]);

  // Estadísticas para debugging
  const stats = useMemo(() => ({
    totalMessages: messages.length,
    isLargeChat: messages.length > 100,
    shouldUseVirtualization: messages.length > 50
  }), [messages.length]);

  return {
    optimizedMessages,
    stats
  };
};
