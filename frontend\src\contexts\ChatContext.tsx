import { createContext, Dispatch, SetStateAction } from 'react';
import { AgentDetails } from '../components/Chat/AgentSelector';

export interface ChatMessage {
  id?: string;
  thread_id: number | string | bigint;
  content: string | null;
  type: string | null;
  from_sender: 'User' | 'Agent' | null;
  message_id: number | null;
  agent_id: string | null;
  user_id: string | null;
  created_at: string;
  intermediate_steps?: ChatMessage[];
  tool_name?: string | null;
  doc_export_status?: 'pending' | 'success' | 'error' | null;
  doc_export_url?: string | null;
  input_token_cost?: number | null;
  output_token_cost?: number | null;
}

export interface ChatContextProps {
  currentThreadId: number | null;
  setCurrentThreadId: Dispatch<SetStateAction<number | null>>;
  messages: ChatMessage[];
  selectedAgentId: string | null;
  setSelectedAgentId: (agentId: string | null) => void;
  isProcessingMessage: boolean;
  isContextPanelOpen: boolean;
  isLoadingHistory: boolean;
  isAgentSelectionRequired: boolean;
  currentThreadTitle: string | null;
  isLoadingTitle: boolean;
  chatInputText: string;
  setChatInputText: Dispatch<SetStateAction<string>>;
  selectedAgentDetails: AgentDetails | null;
  setSelectedAgentDetails: Dispatch<SetStateAction<AgentDetails | null>>;
  totalTokens: number;
  totalCost: number;
  selectedModelName: string | null;
  setSelectedModelName: Dispatch<SetStateAction<string | null>>;
  addMessage: (message: ChatMessage) => void;
  clearMessages: () => void;
  startProcessing: () => void;
  stopProcessing: () => void;
  toggleContextPanel: () => void;
  startNewConversation: () => void;
  updateThreadTitle: (newTitle: string) => Promise<void>;
}

export const ChatContext = createContext<ChatContextProps | undefined>(undefined);