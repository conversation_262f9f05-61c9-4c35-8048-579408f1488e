import React, { useState, useEffect } from 'react';
import { useChat } from '../../hooks/useChat'; // Corrected import path
import { useAuth } from '../../hooks/useAuth'; // Corrected import path
import { API_BASE_URL } from '../../config/api';

// Define a type for the Agent data expected from the backend API
// Based on backend/app/models/agent.py::Agent
export interface Agent {
  id: string; // UUID as string
  nombre: string;
  descripcion?: string | null;
  activo: boolean;
  system_prompt?: string | null; // Added for token calculation
}

// Define types for Tool and Companion based on backend models
export interface Tool {
    id: string;
    tool_name: string;
    tool_description?: string | null;
    // Add other fields if needed
}

// Use the base Agent type for Companions
export type Companion = Agent;

// Define the structure for AgentDetails response
export interface AgentDetails extends Agent {
    tools: Tool[];
    companeros: Companion[];
}


const AgentSelector: React.FC = () => {
  const { selectedAgentId, setSelectedAgentId, selectedAgentDetails } = useChat();
  const { session } = useAuth();
  const [agents, setAgents] = useState<Agent[]>([]);
  const [isLoadingAgents, setIsLoadingAgents] = useState<boolean>(false);
  const [agentsError, setAgentsError] = useState<string | null>(null);
  const [isInfoOpen, setIsInfoOpen] = useState<boolean>(false);

  useEffect(() => {
    const fetchAgents = async () => {
      setIsLoadingAgents(true);
      setAgentsError(null);
      if (!session?.access_token) {
        setAgentsError("Not authenticated.");
        setIsLoadingAgents(false);
        return;
      }
      try {
        const apiUrl = `${API_BASE_URL}/agents?activo=true`;
        const response = await fetch(apiUrl, {
          headers: { 'Authorization': `Bearer ${session.access_token}` },
        });
        if (!response.ok) {
          const errorData = await response.json().catch(() => ({ detail: `HTTP error ${response.status}` }));
          throw new Error(errorData.detail || 'Failed to fetch agents');
        }
        const data: Agent[] = await response.json();
        setAgents(data);
      } catch (err) {
        console.error("Error fetching agents:", err);
        setAgentsError(err instanceof Error ? err.message : 'An unknown error occurred');
      } finally {
        setIsLoadingAgents(false);
      }
    };
    fetchAgents();
  }, [session]);

  const handleSelectChange = (event: React.ChangeEvent<HTMLSelectElement>) => {
    const newAgentId = event.target.value || null;
    setSelectedAgentId(newAgentId);
    setIsInfoOpen(false);
  };

  // Check if current selection is valid
  const isSelectionValid = !selectedAgentId || agents.some(agent => agent.id === selectedAgentId);

  const toggleInfoDropdown = () => setIsInfoOpen(prev => !prev);

  return (
    <div className="space-y-4">
      <div>
        <label htmlFor="agent-select" className="block text-sm font-medium text-gray-700 mb-2">
          Seleccionar Agente
        </label>
        {isLoadingAgents && (
          <div className="flex items-center space-x-2">
            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-indigo-600"></div>
            <p className="text-sm text-gray-500">Cargando agentes...</p>
          </div>
        )}
        {agentsError && (
          <div className="bg-red-50 border border-red-200 rounded-md p-3">
            <p className="text-sm text-red-600">Error: {agentsError}</p>
          </div>
        )}
        {!isLoadingAgents && !agentsError && (
          <>
            <select
              id="agent-select"
              value={isSelectionValid ? (selectedAgentId || '') : ''}
              onChange={handleSelectChange}
              disabled={isLoadingAgents}
              className="block w-full pl-3 pr-10 py-4 text-base border-gray-300 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 rounded-lg shadow-sm disabled:bg-gray-100 transition-colors min-h-[44px]"
            >
              <option value="" disabled={agents.length > 0}>
                {agents.length === 0 ? 'No hay agentes activos' : '-- Selecciona un Agente --'}
              </option>
              {agents.map((agent) => (
                <option key={agent.id} value={agent.id}>
                  {agent.nombre}
                </option>
              ))}
            </select>
            {!isSelectionValid && (
              <div className="mt-2 bg-yellow-50 border border-yellow-200 rounded-md p-2">
                <p className="text-sm text-yellow-600">El agente seleccionado ya no está disponible. Por favor, selecciona otro agente.</p>
              </div>
            )}
          </>
        )}
      </div>

      {/* Agent Info Dropdown */}
      {selectedAgentId && (
        <div>
          <button
            onClick={toggleInfoDropdown}
            disabled={!selectedAgentDetails}
            className="w-full flex items-center justify-between p-4 text-sm font-medium text-indigo-600 hover:text-indigo-800 bg-indigo-50 hover:bg-indigo-100 rounded-lg border border-indigo-200 transition-colors disabled:opacity-50 disabled:cursor-not-allowed min-h-[44px] active:scale-95"
            aria-expanded={isInfoOpen}
            aria-controls="agent-info-content"
          >
            <span>Información del Agente</span>
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className={`h-4 w-4 transition-transform ${isInfoOpen ? 'rotate-180' : ''}`}
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
              strokeWidth={2}
            >
              <path strokeLinecap="round" strokeLinejoin="round" d="M19 9l-7 7-7-7" />
            </svg>
          </button>

          {isInfoOpen && selectedAgentDetails && (
            <div id="agent-info-content" className="mt-3 p-4 border border-gray-200 rounded-lg bg-white shadow-sm space-y-3">
              <div className="space-y-3">
                <div>
                  <p className="text-sm"><span className="font-semibold text-gray-700">Descripción:</span></p>
                  <p className="text-sm text-gray-600 mt-1">{selectedAgentDetails.descripcion || 'Sin descripción disponible'}</p>
                </div>

                <div>
                  <p className="font-semibold text-gray-700 text-sm mb-2">Herramientas:</p>
                  {selectedAgentDetails.tools.length > 0 ? (
                    <div className="space-y-1">
                      {selectedAgentDetails.tools.map(tool => (
                        <div key={tool.id} className="bg-gray-50 rounded-md p-2">
                          <p className="text-sm font-medium text-gray-800">{tool.tool_name}</p>
                          {tool.tool_description && (
                            <p className="text-xs text-gray-600 mt-1">{tool.tool_description}</p>
                          )}
                        </div>
                      ))}
                    </div>
                  ) : (
                    <p className="text-sm text-gray-500 italic">Sin herramientas asociadas</p>
                  )}
                </div>

                <div>
                  <p className="font-semibold text-gray-700 text-sm mb-2">Compañeros:</p>
                  {selectedAgentDetails.companeros.length > 0 ? (
                    <div className="space-y-1">
                      {selectedAgentDetails.companeros.map(comp => (
                        <div key={comp.id} className="bg-gray-50 rounded-md p-2">
                          <p className="text-sm font-medium text-gray-800">{comp.nombre}</p>
                          {comp.descripcion && (
                            <p className="text-xs text-gray-600 mt-1">{comp.descripcion}</p>
                          )}
                        </div>
                      ))}
                    </div>
                  ) : (
                    <p className="text-sm text-gray-500 italic">Sin compañeros asociados</p>
                  )}
                </div>
              </div>
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default AgentSelector;