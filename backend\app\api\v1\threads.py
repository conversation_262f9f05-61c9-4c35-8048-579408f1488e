import logging
from fastapi import API<PERSON>outer, Depends, HTTPException, status
from typing import List
from uuid import UUID

from app.models import chat as chat_models
from app.core.security import get_current_user, CurrentUser
from app.services import thread_service, thread_metadata_service
from app.utils.token_estimator import estimate_tokens_from_messages

logger = logging.getLogger(__name__)
router = APIRouter()

@router.get("/", response_model=List[chat_models.ThreadSummary])
async def get_user_threads(
    current_user: CurrentUser = Depends(get_current_user),
    skip: int = 0,
    limit: int = 50
):
    logger.info(f"User {current_user.id} requesting thread list (skip={skip}, limit={limit})")
    try:
        user_uuid = UUID(current_user.id)
        all_threads = await thread_service.get_threads_for_user(user_id=user_uuid)
        paginated_threads = all_threads[skip : skip + limit]
        logger.info(f"Returning {len(paginated_threads)} thread summaries for user {current_user.id}")
        return paginated_threads
    except ValueError:
        logger.error(f"Invalid user ID format received from token: {current_user.id}")
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="Invalid user ID format.")
    except Exception as e:
        logger.exception(f"Unexpected error fetching threads for user {current_user.id}: {e}")
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="An unexpected error occurred while fetching threads.")

@router.get("/{thread_id}", response_model=List[chat_models.ChatMessage], response_model_by_alias=True)
async def get_thread_messages(
    thread_id: int,
    current_user: CurrentUser = Depends(get_current_user),
    limit: int = 2000,  # Increased limit
    offset: int = 0,
    load_recent: bool = True
):
    logger.info(f"User {current_user.id} requesting messages for thread {thread_id}")
    try:
        user_uuid = UUID(current_user.id)
        is_authorized = await thread_service.check_thread_authorization(thread_id=thread_id, user_id=user_uuid)
        if not is_authorized:
            raise HTTPException(status_code=status.HTTP_403_FORBIDDEN, detail="Not authorized to access this thread")
        
        messages = await thread_service.get_messages_for_thread(
            thread_id=thread_id,
            limit=limit,
            offset=offset,
            load_recent=load_recent
        )
        logger.info(f"Returning {len(messages)} messages for thread {thread_id}")
        return messages
    except HTTPException as http_exc:
        raise http_exc
    except ValueError:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="Invalid user ID format.")
    except Exception as e:
        logger.exception(f"Unexpected error fetching messages for thread {thread_id}: {e}")
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="An unexpected error occurred while fetching thread messages.")

@router.get("/{thread_id}/count", response_model=dict)
async def get_thread_message_count(
    thread_id: int,
    current_user: CurrentUser = Depends(get_current_user)
):
    logger.info(f"User {current_user.id} requesting message count for thread {thread_id}")
    try:
        user_uuid = UUID(current_user.id)
        is_authorized = await thread_service.check_thread_authorization(thread_id=thread_id, user_id=user_uuid)
        if not is_authorized:
            raise HTTPException(status_code=status.HTTP_403_FORBIDDEN, detail="Not authorized to access this thread")
        count = await thread_service.get_thread_message_count(thread_id=thread_id)
        return {"thread_id": thread_id, "message_count": count}
    except Exception as e:
        logger.exception(f"Unexpected error getting message count for thread {thread_id}: {e}")
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="An unexpected error occurred.")

@router.get("/{thread_id}/metadata", response_model=chat_models.ThreadMetadata)
async def get_thread_metadata(
    thread_id: int,
    current_user: CurrentUser = Depends(get_current_user)
):
    logger.info(f"User {current_user.id} requesting metadata for thread {thread_id}")
    try:
        user_uuid = UUID(current_user.id)
        is_authorized = await thread_service.check_thread_authorization(thread_id=thread_id, user_id=user_uuid)
        if not is_authorized:
            raise HTTPException(status_code=status.HTTP_403_FORBIDDEN, detail="Not authorized to access this thread")
        metadata = await thread_metadata_service.get_thread_metadata(thread_id=thread_id, user_token=current_user.token)
        if not metadata:
            metadata = await thread_metadata_service.ensure_thread_metadata_exists(thread_id, user_token=current_user.token)
        return metadata
    except Exception as e:
        logger.exception(f"Unexpected error getting metadata for thread {thread_id}: {e}")
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="An unexpected error occurred.")

@router.put("/{thread_id}/metadata", response_model=chat_models.ThreadMetadata)
async def update_thread_metadata(
    thread_id: int,
    metadata_update: chat_models.ThreadMetadataUpdate,
    current_user: CurrentUser = Depends(get_current_user)
):
    logger.info(f"User {current_user.id} updating metadata for thread {thread_id}")
    try:
        user_uuid = UUID(current_user.id)
        is_authorized = await thread_service.check_thread_authorization(thread_id=thread_id, user_id=user_uuid)
        if not is_authorized:
            raise HTTPException(status_code=status.HTTP_403_FORBIDDEN, detail="Not authorized to access this thread")
        updated_metadata = await thread_metadata_service.update_thread_metadata(thread_id=thread_id, metadata_update=metadata_update, user_token=current_user.token)
        return updated_metadata
    except Exception as e:
        logger.exception(f"Unexpected error updating metadata for thread {thread_id}: {e}")
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="An unexpected error occurred.")

@router.delete("/{thread_id}")
async def delete_thread(
    thread_id: int,
    current_user: CurrentUser = Depends(get_current_user)
):
    logger.info(f"User {current_user.id} requesting deletion of thread {thread_id}")
    try:
        user_uuid = UUID(current_user.id)
        is_authorized = await thread_service.check_thread_authorization(thread_id=thread_id, user_id=user_uuid)
        if not is_authorized:
            raise HTTPException(status_code=status.HTTP_403_FORBIDDEN, detail="Not authorized to delete this thread")
        deleted_count = await thread_service.delete_entire_thread(thread_id=thread_id, user_id=user_uuid)
        return {"message": "Thread deleted successfully", "thread_id": thread_id, "deleted_messages": deleted_count}
    except Exception as e:
        logger.exception(f"Unexpected error deleting thread {thread_id}: {e}")
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="An unexpected error occurred.")


@router.get("/{thread_id}/tokens")
async def get_thread_tokens(
    thread_id: int,
    current_user: CurrentUser = Depends(get_current_user)
):
    """
    Calcula el número total de tokens para un thread específico
    """
    logger.info(f"User {current_user.id} requesting token count for thread {thread_id}")
    try:
        user_uuid = UUID(current_user.id)
        is_authorized = await thread_service.check_thread_authorization(thread_id=thread_id, user_id=user_uuid)
        if not is_authorized:
            raise HTTPException(status_code=status.HTTP_403_FORBIDDEN, detail="Not authorized to access this thread")

        # Obtener todos los mensajes del thread
        messages = await thread_service.get_messages_for_thread(
            thread_id=thread_id,
            limit=10000,  # Obtener todos los mensajes
            offset=0,
            load_recent=False
        )

        # TODO: Obtener system_prompt y tools del agente si es necesario
        # Por ahora calculamos solo con los mensajes
        total_tokens = estimate_tokens_from_messages(messages)

        # Calcular costo total sumando input_token_cost y output_token_cost
        total_cost = 0.0
        for message in messages:
            input_cost = float(message.input_token_cost) if message.input_token_cost is not None else 0.0
            output_cost = float(message.output_token_cost) if message.output_token_cost is not None else 0.0
            total_cost += input_cost + output_cost

        return {
            "thread_id": thread_id,
            "total_tokens": total_tokens,
            "total_cost": round(total_cost, 5),  # Redondear a 5 decimales
            "message_count": len(messages)
        }

    except Exception as e:
        logger.exception(f"Unexpected error calculating tokens for thread {thread_id}: {e}")
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="An unexpected error occurred.")