import { useState, useEffect } from 'react';

interface MobileDetectionResult {
  isMobile: boolean;
  isTablet: boolean;
  isDesktop: boolean;
  isTouchDevice: boolean;
  isIOS: boolean;
  isAndroid: boolean;
  screenWidth: number;
  screenHeight: number;
}

/**
 * Hook for reliable mobile device detection
 * Combines multiple detection methods for better accuracy
 */
export const useMobileDetection = (): MobileDetectionResult => {
  const [detection, setDetection] = useState<MobileDetectionResult>(() => {
    // Initial detection on mount
    return detectDevice();
  });

  useEffect(() => {
    const handleResize = () => {
      setDetection(detectDevice());
    };

    const handleOrientationChange = () => {
      // Delay to allow for orientation change to complete
      setTimeout(() => {
        setDetection(detectDevice());
      }, 100);
    };

    // Listen for resize and orientation changes
    window.addEventListener('resize', handleResize);
    window.addEventListener('orientationchange', handleOrientationChange);

    // Initial detection after mount
    setDetection(detectDevice());

    return () => {
      window.removeEventListener('resize', handleResize);
      window.removeEventListener('orientationchange', handleOrientationChange);
    };
  }, []);

  return detection;
};

/**
 * Comprehensive device detection function
 */
function detectDevice(): MobileDetectionResult {
  const userAgent = navigator.userAgent || '';
  const screenWidth = window.innerWidth;
  const screenHeight = window.innerHeight;

  // Touch device detection
  const isTouchDevice = 'ontouchstart' in window ||
                       navigator.maxTouchPoints > 0 ||
                       ('msMaxTouchPoints' in navigator && (navigator as { msMaxTouchPoints: number }).msMaxTouchPoints > 0);

  // OS detection
  const isIOS = /iPad|iPhone|iPod/.test(userAgent) || 
                (navigator.platform === 'MacIntel' && navigator.maxTouchPoints > 1);
  const isAndroid = /Android/.test(userAgent);

  // User agent based mobile detection
  const mobileUserAgents = [
    /Android/i,
    /webOS/i,
    /iPhone/i,
    /iPad/i,
    /iPod/i,
    /BlackBerry/i,
    /Windows Phone/i,
    /Opera Mini/i,
    /IEMobile/i,
    /Mobile/i,
    /mobile/i,
    /CriOS/i
  ];

  const isMobileUserAgent = mobileUserAgents.some(regex => regex.test(userAgent));

  // Screen size based detection
  const isMobileScreen = screenWidth <= 768;
  const isTabletScreen = screenWidth > 768 && screenWidth <= 1024;

  // Combined mobile detection
  const isMobile = isMobileUserAgent || (isMobileScreen && isTouchDevice);
  const isTablet = (isTabletScreen && isTouchDevice) || 
                   (/iPad/.test(userAgent) || (navigator.platform === 'MacIntel' && navigator.maxTouchPoints > 1));
  const isDesktop = !isMobile && !isTablet;

  return {
    isMobile,
    isTablet,
    isDesktop,
    isTouchDevice,
    isIOS,
    isAndroid,
    screenWidth,
    screenHeight
  };
}

/**
 * Simple hook for just mobile detection
 */
export const useIsMobile = (): boolean => {
  const { isMobile } = useMobileDetection();
  return isMobile;
};

/**
 * Hook for responsive breakpoints
 */
export const useResponsiveBreakpoints = () => {
  const { screenWidth } = useMobileDetection();
  
  return {
    isXs: screenWidth < 480,
    isSm: screenWidth >= 480 && screenWidth < 640,
    isMd: screenWidth >= 640 && screenWidth < 768,
    isLg: screenWidth >= 768 && screenWidth < 1024,
    isXl: screenWidth >= 1024 && screenWidth < 1280,
    is2Xl: screenWidth >= 1280,
    screenWidth
  };
};

export default useMobileDetection;
