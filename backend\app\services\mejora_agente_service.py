from typing import List, Optional, Dict, Any
from uuid import UUID
from datetime import datetime
import math
import aiohttp
import json

from app.core.database import get_supabase_client
from app.core.config import get_settings
from app.models.mejora_agente import (
    MejoraAgente,
    MejoraAgenteCreate,
    MejoraAgenteUpdate,
    MejoraAgenteListItem,
    MejoraAgenteDetail,
    MejoraAgenteListResponse,
    MejoraAgenteFilters,
    MejoraAgenteSortBy,
    MejoraAgenteApplyRequest,
    MejoraAgenteApplyResponse,
    PromptComparison,
    MejoraAgenteStats,
    EstadoMejoraAgente
)

class MejoraAgenteService:
    """Servicio para manejar operaciones de mejoras de agente."""

    def __init__(self):
        self.supabase = None
        self.settings = get_settings()
    
    async def create_mejora_agente(self, mejora_data: MejoraAgenteCreate, user_id: UUID) -> MejoraAgente:
        """Crear una nueva mejora de agente."""
        try:
            supabase = await get_supabase_client()
            # Preparar datos para inserción
            insert_data = mejora_data.model_dump()
            insert_data['user_id'] = str(user_id)  # Agregar user_id si es necesario

            result = supabase.table('mejoras_agentes').insert(insert_data).execute()
            
            if not result.data:
                raise Exception("Error al crear la mejora de agente")
            
            return MejoraAgente(**result.data[0])
        except Exception as e:
            raise Exception(f"Error al crear mejora de agente: {str(e)}")
    
    async def get_mejora_agente_by_id(self, mejora_id: UUID, user_id: UUID) -> Optional[MejoraAgenteDetail]:
        """Obtener una mejora de agente por ID con detalles completos."""
        try:
            supabase = await get_supabase_client()
            # Obtener la mejora
            result = supabase.table('mejoras_agentes').select('*').eq('id', str(mejora_id)).execute()

            if not result.data:
                return None

            mejora_data = result.data[0]

            # Obtener información adicional del agente (prompts originales)
            agente_result = supabase.table('workflows').select(
                'system_prompt_plantilla, user_prompt_plantilla, nombre'
            ).eq('id', mejora_data['agente_id']).execute()

            # Obtener evaluaciones relacionadas
            evaluaciones_result = supabase.table('evaluaciones').select('id').eq(
                'mejora_agente_id', str(mejora_id)
            ).execute()
            
            # Construir respuesta detallada
            mejora_detail = MejoraAgenteDetail(**mejora_data)
            
            if agente_result.data:
                agente_info = agente_result.data[0]
                mejora_detail.system_prompt_original = agente_info.get('system_prompt_plantilla')
                mejora_detail.user_prompt_original = agente_info.get('user_prompt_plantilla')
                mejora_detail.nombre_agente_amigable = agente_info.get('nombre')
            
            if evaluaciones_result.data:
                mejora_detail.evaluaciones_relacionadas = [ev['id'] for ev in evaluaciones_result.data]
            
            return mejora_detail
        except Exception as e:
            raise Exception(f"Error al obtener mejora de agente: {str(e)}")
    
    async def update_mejora_agente(self, mejora_id: UUID, mejora_data: MejoraAgenteUpdate, user_id: UUID) -> Optional[MejoraAgente]:
        """Actualizar una mejora de agente."""
        try:
            supabase = await get_supabase_client()
            # Filtrar campos no nulos
            update_data = {k: v for k, v in mejora_data.model_dump().items() if v is not None}

            if not update_data:
                # Si no hay datos para actualizar, obtener la mejora actual
                result = supabase.table('mejoras_agentes').select('*').eq('id', str(mejora_id)).execute()
                if result.data:
                    return MejoraAgente(**result.data[0])
                return None

            # Agregar timestamp de actualización
            update_data['updated_at'] = datetime.utcnow().isoformat()

            result = supabase.table('mejoras_agentes').update(update_data).eq('id', str(mejora_id)).execute()
            
            if not result.data:
                return None
            
            return MejoraAgente(**result.data[0])
        except Exception as e:
            raise Exception(f"Error al actualizar mejora de agente: {str(e)}")
    
    async def delete_mejora_agente(self, mejora_id: UUID, user_id: UUID) -> bool:
        """Eliminar una mejora de agente."""
        try:
            supabase = await get_supabase_client()
            result = supabase.table('mejoras_agentes').delete().eq('id', str(mejora_id)).execute()
            return len(result.data) > 0
        except Exception as e:
            raise Exception(f"Error al eliminar mejora de agente: {str(e)}")
    
    async def list_mejoras_agentes(
        self,
        user_id: UUID,
        page: int = 1,
        page_size: int = 20,
        filters: Optional[MejoraAgenteFilters] = None,
        sort_by: MejoraAgenteSortBy = MejoraAgenteSortBy.FECHA_CREACION_DESC
    ) -> MejoraAgenteListResponse:
        """Listar mejoras de agente con filtros, ordenamiento y paginación."""
        try:
            supabase = await get_supabase_client()
            # Construir query base
            query = supabase.table('mejoras_agentes').select(
                'id, agente_id, n8n_workflow_id, explicacion_mejoras, estado, '
                'created_at, updated_at',
                count='exact'
            )
            
            # Aplicar filtros
            if filters:
                if filters.estado:
                    query = query.in_('estado', [estado.value for estado in filters.estado])
                if filters.agente_id:
                    query = query.in_('agente_id', [str(aid) for aid in filters.agente_id])
                if filters.n8n_workflow_id:
                    query = query.in_('n8n_workflow_id', filters.n8n_workflow_id)
                if filters.fecha_desde:
                    query = query.gte('created_at', filters.fecha_desde.isoformat())
                if filters.fecha_hasta:
                    query = query.lte('created_at', filters.fecha_hasta.isoformat())
            
            # Aplicar ordenamiento
            order_column, order_direction = self._parse_sort_by(sort_by)
            query = query.order(order_column, desc=(order_direction == 'desc'))
            
            # Aplicar paginación
            offset = (page - 1) * page_size
            query = query.range(offset, offset + page_size - 1)
            
            result = query.execute()
            
            # Convertir resultados y agregar información adicional
            mejoras = []
            for item in result.data:
                mejora_item = MejoraAgenteListItem(**item)
                
                # Obtener nombre amigable del agente
                agente_result = supabase.table('workflows').select('nombre').eq('id', item['agente_id']).execute()
                if agente_result.data:
                    mejora_item.nombre_agente_amigable = agente_result.data[0]['nombre']

                # Contar evaluaciones relacionadas
                eval_count_result = supabase.table('evaluaciones').select('id', count='exact').eq(
                    'mejora_agente_id', item['id']
                ).execute()
                mejora_item.evaluaciones_relacionadas = eval_count_result.count or 0
                
                mejoras.append(mejora_item)
            
            total = result.count or 0
            total_pages = math.ceil(total / page_size)
            
            return MejoraAgenteListResponse(
                mejoras=mejoras,
                total=total,
                page=page,
                page_size=page_size,
                total_pages=total_pages
            )
        except Exception as e:
            raise Exception(f"Error al listar mejoras de agente: {str(e)}")
    
    async def apply_mejora_agente(self, apply_request: MejoraAgenteApplyRequest, user_id: UUID) -> MejoraAgenteApplyResponse:
        """Aplicar una mejora de agente vía webhook n8n."""
        try:
            # Obtener la mejora
            mejora_detail = await self.get_mejora_agente_by_id(apply_request.mejora_id, user_id)
            if not mejora_detail:
                raise Exception("Mejora de agente no encontrada")
            
            if mejora_detail.estado != EstadoMejoraAgente.PENDIENTE_REVISION_HUMANA:
                raise Exception(f"La mejora no está en estado pendiente de revisión: {mejora_detail.estado}")
            
            # Preparar payload para n8n webhook
            webhook_payload = {
                "mejora_id": str(mejora_detail.id),
                "agente_id": str(mejora_detail.agente_id),
                "n8n_workflow_id": mejora_detail.n8n_workflow_id,
                "system_prompt_propuesto": mejora_detail.system_prompt_propuesto,
                "user_prompt_propuesto": mejora_detail.user_prompt_propuesto,
                "explicacion_mejoras": mejora_detail.explicacion_mejoras
            }
            
            # Llamar al webhook n8n
            webhook_url = "https://automatizaciones.aceleralia.com/webhook-test/849d11d1-0c68-4cc4-b9b9-b3d8a02ef818"
            
            async with aiohttp.ClientSession() as session:
                async with session.post(
                    webhook_url,
                    json=webhook_payload,
                    headers={'Content-Type': 'application/json'},
                    timeout=aiohttp.ClientTimeout(total=30)
                ) as response:
                    webhook_response = await response.json() if response.content_type == 'application/json' else await response.text()
                    
                    if response.status != 200:
                        raise Exception(f"Error en webhook n8n: {response.status} - {webhook_response}")
            
            # Actualizar estado de la mejora a aprobado
            mejora_actualizada = await self.update_mejora_agente(
                apply_request.mejora_id,
                MejoraAgenteUpdate(estado=EstadoMejoraAgente.APROBADO_PARA_APLICAR),
                user_id
            )
            
            return MejoraAgenteApplyResponse(
                success=True,
                message="Mejora aplicada exitosamente",
                webhook_response=webhook_response,
                mejora_actualizada=mejora_actualizada
            )
        except Exception as e:
            return MejoraAgenteApplyResponse(
                success=False,
                message=f"Error al aplicar mejora: {str(e)}",
                webhook_response=None,
                mejora_actualizada=None
            )
    
    async def get_prompt_comparison(self, mejora_id: UUID, user_id: UUID) -> Optional[PromptComparison]:
        """Obtener comparación de prompts para una mejora."""
        try:
            mejora_detail = await self.get_mejora_agente_by_id(mejora_id, user_id)
            if not mejora_detail:
                return None
            
            return PromptComparison(
                system_prompt_original=mejora_detail.system_prompt_original,
                system_prompt_propuesto=mejora_detail.system_prompt_propuesto,
                user_prompt_original=mejora_detail.user_prompt_original,
                user_prompt_propuesto=mejora_detail.user_prompt_propuesto,
                explicacion_mejoras=mejora_detail.explicacion_mejoras,
                agente_id=mejora_detail.agente_id,
                nombre_agente_amigable=mejora_detail.nombre_agente_amigable
            )
        except Exception as e:
            raise Exception(f"Error al obtener comparación de prompts: {str(e)}")
    
    def _parse_sort_by(self, sort_by: MejoraAgenteSortBy) -> tuple[str, str]:
        """Parsear el campo de ordenamiento."""
        sort_mapping = {
            MejoraAgenteSortBy.FECHA_CREACION_ASC: ('created_at', 'asc'),
            MejoraAgenteSortBy.FECHA_CREACION_DESC: ('created_at', 'desc'),
            MejoraAgenteSortBy.FECHA_ACTUALIZACION_ASC: ('updated_at', 'asc'),
            MejoraAgenteSortBy.FECHA_ACTUALIZACION_DESC: ('updated_at', 'desc'),
            MejoraAgenteSortBy.ESTADO_ASC: ('estado', 'asc'),
            MejoraAgenteSortBy.ESTADO_DESC: ('estado', 'desc'),
        }
        return sort_mapping.get(sort_by, ('created_at', 'desc'))
