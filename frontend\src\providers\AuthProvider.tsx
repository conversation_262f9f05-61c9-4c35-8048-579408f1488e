import React, { useState, useEffect, ReactNode, useCallback, useMemo } from 'react';
import { Session, User, AuthChangeEvent, AuthSession } from '@supabase/supabase-js';
import { supabase } from '../services/supabaseClient';
import { AuthContext, AuthContextType } from '../contexts/AuthContext'; // Import context and type

// Define the props for the provider component
interface AuthProviderProps {
  children: ReactNode;
}

// Create the provider component
export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [session, setSession] = useState<Session | null>(null);
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState<boolean>(true); // Start as loading

  useEffect(() => {
    setIsLoading(true);
    // 1. Get initial session
    supabase.auth.getSession().then(({ data: { session } }: { data: { session: Session | null } }) => {
      setSession(session);
      setUser(session?.user ?? null);
      setIsLoading(false);
    }).catch((error: unknown) => {
        console.error("Error getting initial session:", error);
        setIsLoading(false);
    });

    // 2. Set up auth state change listener
    const { data: authListener } = supabase.auth.onAuthStateChange(
      async (event: AuthChangeEvent, session: AuthSession | null) => {
        console.log('Auth state changed:', event, session);
        setSession(session);
        setUser(session?.user ?? null);
        if (event === 'INITIAL_SESSION') {
            setIsLoading(false);
        }
      }
    );

    // 3. Cleanup listener on unmount
    return () => {
      authListener?.subscription.unsubscribe();
    };
  }, []);

  // Login function - memoized to prevent unnecessary re-renders
  const login = useCallback(async (email: string, password: string) => {
    setIsLoading(true);
    console.log("Login attempt...");
    const { error } = await supabase.auth.signInWithPassword({ email, password });
    if (error) {
      console.error("Login error:", error.message);
    } else {
      console.log("Login successful (listener will update state)");
    }
    setIsLoading(false);
    return { error: error || null };
  }, []);

  // Logout function - memoized to prevent unnecessary re-renders
  const logout = useCallback(async () => {
    setIsLoading(true);
    console.log("Logout attempt...");
    const { error } = await supabase.auth.signOut();
    if (error) {
      console.error("Logout error:", error.message);
    } else {
      console.log("Logout successful (listener will update state)");
      setSession(null);
      setUser(null);
    }
    setIsLoading(false);
    return { error: error || null };
  }, []);

  // Function to get the current token
  const getToken = useCallback(async () => {
    const { data } = await supabase.auth.getSession();
    return data.session?.access_token || null;
  }, []);

  // Memoize the context value to prevent unnecessary re-renders
  const value: AuthContextType = useMemo(() => ({
    session,
    user,
    isAuthenticated: !!session,
    isLoading,
    login,
    logout,
    getToken,
  }), [session, user, isLoading, login, logout, getToken]);

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};

// Default export is not strictly necessary but can be conventional
// export default AuthProvider;