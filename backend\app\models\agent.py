from pydantic import BaseModel, Field
from typing import Optional, List
from uuid import UUID
from datetime import datetime
from decimal import Decimal # For temperature
from .tool import Tool # Import Tool model

# Forward declaration for Tool removed as we import it directly

class AgentBase(BaseModel):
    """Base model for agent data."""
    nombre: str
    descripcion: Optional[str] = None
    system_prompt: str
    activo: bool = True
    selected_llm_model_id: Optional[UUID] = None # Assuming this links to another table eventually
    db_schema: Optional[bool] = None # Consider if this needs more structure
    temperature: Optional[Decimal] = None

class AgentCreate(AgentBase):
    """Model for creating a new agent."""
    pass

class AgentUpdate(BaseModel):
    """Model for updating agent data."""
    nombre: Optional[str] = None
    descripcion: Optional[str] = None
    system_prompt: Optional[str] = None
    activo: Optional[bool] = None
    selected_llm_model_id: Optional[UUID] = None
    db_schema: Optional[bool] = None
    temperature: Optional[Decimal] = None

class AgentInDBBase(AgentBase):
    """Base model for agent data stored in the database."""
    id: UUID
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True # Pydantic V2 alias for orm_mode

class Agent(AgentInDBBase):
    """Model representing an agent as returned from the API (potentially with relations)."""
    # Basic Agent model without relations for general use
    pass

class AgentInDB(AgentInDBBase):
    """Model representing an agent stored in the database."""
    pass

# Models for relationship tables (primarily for API responses if needed)
class AgentToolLink(BaseModel):
    agente_id: UUID
    tool_id: UUID
    created_at: Optional[datetime] = None
    id: UUID

class AgentCompanionLink(BaseModel):
    agente_id: UUID
    companero_id: UUID
    created_at: Optional[datetime] = None
    id: UUID

# Specific model for the get_agent_details response
class AgentDetails(Agent):
    """Model representing an agent with its associated tools and companions."""
    system_prompt: str
    tools: List[Tool] = Field(default_factory=list)
    companeros: List['Agent'] = Field(default_factory=list) # Recursive relationship, use Agent model

# Update forward reference if needed (Pydantic v2 handles this better)
# AgentDetails.update_forward_refs() # Not usually needed in Pydantic v2+