import { defineConfig, loadEnv } from 'vite'
import react from '@vitejs/plugin-react-swc'
import tailwindcss from '@tailwindcss/vite' // Import the new Vite plugin
import wasm from 'vite-plugin-wasm'

// https://vite.dev/config/
export default defineConfig(({ command, mode }) => {
  // Load only VITE_ prefixed environment variables for security
  const env = loadEnv(mode, process.cwd(), 'VITE_')

  // Only log in development mode and avoid sensitive information
  if (mode === 'development') {
    console.log(`🔧 Vite Config - Mode: ${mode}, Command: ${command}`)
    console.log(`🔧 VITE_API_BASE_URL: ${env.VITE_API_BASE_URL}`)
    console.log(`🔧 VITE_API_URL: ${env.VITE_API_URL}`)
    console.log(`🔧 VITE_ENVIRONMENT: ${env.VITE_ENVIRONMENT}`)
  }

  return {
    plugins: [
      react(),
      tailwindcss(), // Use the Tailwind Vite plugin
      wasm(), // Support for WebAssembly modules
    ],
    // Define global constants that can be replaced at build time
    define: {
      __APP_ENV__: JSON.stringify(env.VITE_ENVIRONMENT),
      __API_URL__: JSON.stringify(env.VITE_API_BASE_URL || env.VITE_API_URL),
    },
    // Server configuration for development
    server: {
      port: 5173,
      host: true, // Allow external connections
    },
    // Build configuration
    build: {
      outDir: 'dist',
      sourcemap: mode === 'development',
      target: 'esnext', // Support for top-level await and modern features
      // Ensure environment variables are available during build
      rollupOptions: {
        output: {
          manualChunks: {
            vendor: ['react', 'react-dom'],
            supabase: ['@supabase/supabase-js'],
          },
        },
      },
    },
  }
})
