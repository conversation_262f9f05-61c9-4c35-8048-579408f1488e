from fastapi import APIRouter, Depends, HTTPException, Query
from typing import List, Optional
from uuid import UUID

from app.core.security import get_current_user_payload
from typing import Dict, Any
from app.models.evaluacion import (
    Evaluacion,
    EvaluacionCreate,
    EvaluacionUpdate,
    EvaluacionListResponse,
    EvaluacionFilters,
    EvaluacionSortBy,
    EvaluacionGroupBy,
    EvaluacionBatchUpdate,
    EvaluacionStats,
    EvaluacionDashboardData,
    EstadoEvaluacion
)
from app.services.evaluacion_service import EvaluacionService

router = APIRouter()

@router.post("/", response_model=Evaluacion)
async def create_evaluacion(
    evaluacion_data: EvaluacionCreate,
    current_user: Dict[str, Any] = Depends(get_current_user_payload)
):
    """Crear una nueva evaluación."""
    try:
        service = EvaluacionService()
        return await service.create_evaluacion(evaluacion_data, current_user["sub"])
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))

@router.get("/", response_model=EvaluacionListResponse)
async def list_evaluaciones(
    page: int = Query(1, ge=1, description="Número de página"),
    page_size: int = Query(20, ge=1, le=100, description="Tamaño de página"),
    # Filtros
    estado: Optional[List[EstadoEvaluacion]] = Query(None, description="Filtrar por estado"),
    agente_id: Optional[List[str]] = Query(None, description="Filtrar por ID de agente"),
    puntuacion_min: Optional[float] = Query(None, ge=0, le=10, description="Puntuación mínima"),
    puntuacion_max: Optional[float] = Query(None, ge=0, le=10, description="Puntuación máxima"),
    fecha_desde: Optional[str] = Query(None, description="Fecha desde (ISO format)"),
    fecha_hasta: Optional[str] = Query(None, description="Fecha hasta (ISO format)"),
    # Ordenamiento y agrupación
    sort_by: EvaluacionSortBy = Query(EvaluacionSortBy.FECHA_DESC, description="Ordenar por"),
    group_by: EvaluacionGroupBy = Query(EvaluacionGroupBy.NINGUNO, description="Agrupar por"),
    current_user: Dict[str, Any] = Depends(get_current_user_payload)
):
    """Listar evaluaciones con filtros, ordenamiento y paginación."""
    try:
        # Construir filtros
        filters = None
        if any([estado, agente_id, puntuacion_min, puntuacion_max, fecha_desde, fecha_hasta]):
            from datetime import datetime
            filters = EvaluacionFilters(
                estado=estado,
                agente_id=agente_id,
                puntuacion_min=puntuacion_min,
                puntuacion_max=puntuacion_max,
                fecha_desde=datetime.fromisoformat(fecha_desde) if fecha_desde else None,
                fecha_hasta=datetime.fromisoformat(fecha_hasta) if fecha_hasta else None
            )
        
        service = EvaluacionService()
        return await service.list_evaluaciones(
            current_user["sub"], page, page_size, filters, sort_by, group_by
        )
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))

@router.get("/{evaluacion_id}", response_model=Evaluacion)
async def get_evaluacion(
    evaluacion_id: int,
    current_user: Dict[str, Any] = Depends(get_current_user_payload)
):
    """Obtener una evaluación por ID."""
    try:
        service = EvaluacionService()
        evaluacion = await service.get_evaluacion_by_id(evaluacion_id, current_user["sub"])
        if not evaluacion:
            raise HTTPException(status_code=404, detail="Evaluación no encontrada")
        return evaluacion
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))

@router.put("/{evaluacion_id}", response_model=Evaluacion)
async def update_evaluacion(
    evaluacion_id: int,
    evaluacion_data: EvaluacionUpdate,
    current_user: Dict[str, Any] = Depends(get_current_user_payload)
):
    """Actualizar una evaluación."""
    try:
        service = EvaluacionService()
        evaluacion = await service.update_evaluacion(evaluacion_id, evaluacion_data, current_user["sub"])
        if not evaluacion:
            raise HTTPException(status_code=404, detail="Evaluación no encontrada")
        return evaluacion
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))

@router.delete("/{evaluacion_id}")
async def delete_evaluacion(
    evaluacion_id: int,
    current_user: Dict[str, Any] = Depends(get_current_user_payload)
):
    """Eliminar una evaluación."""
    try:
        service = EvaluacionService()
        success = await service.delete_evaluacion(evaluacion_id, current_user["sub"])
        if not success:
            raise HTTPException(status_code=404, detail="Evaluación no encontrada")
        return {"message": "Evaluación eliminada exitosamente"}
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))

@router.patch("/batch-update", response_model=List[Evaluacion])
async def batch_update_evaluaciones(
    batch_update: EvaluacionBatchUpdate,
    current_user: Dict[str, Any] = Depends(get_current_user_payload)
):
    """Actualizar múltiples evaluaciones en lote."""
    try:
        service = EvaluacionService()
        return await service.batch_update_evaluaciones(batch_update, current_user["sub"])
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))

@router.get("/stats/dashboard", response_model=EvaluacionStats)
async def get_evaluacion_stats(
    current_user: Dict[str, Any] = Depends(get_current_user_payload)
):
    """Obtener estadísticas de evaluaciones para el dashboard."""
    try:
        service = EvaluacionService()
        return await service.get_evaluacion_stats(current_user["sub"])
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))

@router.get("/dashboard/charts")
async def get_dashboard_charts_data(
    current_user: Dict[str, Any] = Depends(get_current_user_payload)
):
    """Obtener datos para los gráficos del dashboard agrupados por workflow y agente."""
    try:
        service = EvaluacionService()
        return await service.get_dashboard_charts_data(current_user["sub"])
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))

@router.post("/{evaluacion_id}/mark-reviewed")
async def mark_evaluacion_reviewed(
    evaluacion_id: int,
    current_user: Dict[str, Any] = Depends(get_current_user_payload)
):
    """Marcar una evaluación como revisada."""
    try:
        service = EvaluacionService()
        evaluacion = await service.update_evaluacion(
            evaluacion_id,
            EvaluacionUpdate(estado=EstadoEvaluacion.REVISADO),
            current_user["sub"]
        )
        if not evaluacion:
            raise HTTPException(status_code=404, detail="Evaluación no encontrada")
        return {"message": "Evaluación marcada como revisada", "evaluacion": evaluacion}
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))

@router.post("/{evaluacion_id}/discard")
async def discard_evaluacion(
    evaluacion_id: int,
    current_user: Dict[str, Any] = Depends(get_current_user_payload)
):
    """Descartar una evaluación (cambiar estado a un estado final)."""
    try:
        service = EvaluacionService()
        # En este caso, podríamos usar un estado específico para "descartado"
        # o simplemente marcar como revisado
        evaluacion = await service.update_evaluacion(
            evaluacion_id,
            EvaluacionUpdate(estado=EstadoEvaluacion.REVISADO),
            current_user["sub"]
        )
        if not evaluacion:
            raise HTTPException(status_code=404, detail="Evaluación no encontrada")
        return {"message": "Evaluación descartada", "evaluacion": evaluacion}
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))
