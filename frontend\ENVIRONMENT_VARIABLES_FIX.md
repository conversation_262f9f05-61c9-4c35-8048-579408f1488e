# Fix para Variables de Entorno - Frontend

## Problema Identificado

La aplicación estaba usando `localhost:8000` en producción en lugar de la URL de producción configurada en Coolify. Esto se debía a:

1. **Inconsistencia en nombres de variables**: El código usaba `VITE_API_URL` pero Coolify tenía configurado `VITE_API_BASE_URL`
2. **Uso directo de variables de entorno** en lugar de configuración centralizada
3. **Falta de compatibilidad** entre las dos convenciones de nombres

## Cambios Realizados

### 1. Configuración Centralizada
- ✅ Creado `frontend/src/config/api.ts` para centralizar la configuración de API
- ✅ Soporte para ambas variables: `VITE_API_BASE_URL` (preferida) y `VITE_API_URL` (legacy)
- ✅ Debug mejorado para troubleshooting

### 2. Archivos Actualizados
- ✅ `frontend/src/lib/api.ts` - Usa configuración centralizada
- ✅ `frontend/src/components/Meetings/SelectExistingEntityModal.tsx`
- ✅ `frontend/src/components/Chat/AgentSelector.tsx`
- ✅ `frontend/src/pages/HistoryPage.tsx`
- ✅ `frontend/src/components/Chat/MessageBubble.tsx`
- ✅ `frontend/src/components/Chat/ChatInput.tsx`
- ✅ `frontend/src/hooks/useThreadHistory.ts`
- ✅ `frontend/Dockerfile` - Actualizado para usar `VITE_API_BASE_URL`
- ✅ `frontend/vite.config.ts` - Soporte para ambas variables
- ✅ `frontend/src/vite-env.d.ts` - Tipos TypeScript actualizados
- ✅ `frontend/.env.example` - Documentación actualizada
- ✅ `frontend/.env` - Archivo local actualizado
- ✅ `frontend/DEPLOYMENT.md` - Documentación de deployment actualizada

### 3. Configuración de Variables
La nueva configuración soporta ambas convenciones:
```typescript
// Prioridad: VITE_API_BASE_URL > VITE_API_URL > localhost fallback
export const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || import.meta.env.VITE_API_URL || 'http://localhost:8000';
```

## Instrucciones para Coolify

### Variables de Entorno Actuales (Correctas)
Las variables que ya tienes configuradas en Coolify están correctas:

```
VITE_API_BASE_URL=https://backend.aceleralia.com/api/v1
VITE_ENVIRONMENT=production
VITE_SUPABASE_ANON_KEY=eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJzdXBhYmFzZSIsImlhdCI6MTc0MTE4OTQ0MCwiZXhwIjo0ODk2ODYzMDQwLCJyb2xlIjoiYW5vbiJ9.1ul6hLeDwOhMPosVltTeRJaoGpLYonC1rzlxUYdsGXg
VITE_SUPABASE_URL=https://aceleralia-database.aceleralia.com/
```

### Pasos para Aplicar el Fix

1. **Hacer commit y push de los cambios**:
   ```bash
   git add .
   git commit -m "Fix: Centralizar configuración de API y corregir variables de entorno"
   git push
   ```

2. **Rebuild en Coolify**:
   - Ve a tu aplicación frontend en Coolify
   - Haz clic en "Deploy" o "Rebuild"
   - Las variables de entorno ya están correctamente configuradas

3. **Verificar el Fix**:
   - Una vez deployado, abre la consola del navegador en la aplicación
   - Deberías ver logs como:
     ```
     🔧 API Configuration Debug:
     VITE_API_BASE_URL: https://backend.aceleralia.com/api/v1
     VITE_ENVIRONMENT: production
     API_BASE_URL (resolved): https://backend.aceleralia.com/api/v1
     ```
   - **NO** deberías ver warnings sobre localhost en producción

## Debug y Troubleshooting

### Logs a Verificar
Después del deployment, verifica en la consola del navegador:

1. **Configuración correcta**:
   ```
   🔧 API Configuration Debug:
   VITE_API_BASE_URL: https://backend.aceleralia.com/api/v1
   API_BASE_URL (resolved): https://backend.aceleralia.com/api/v1
   ```

2. **Sin warnings**:
   - No debería aparecer: `⚠️ WARNING: Using localhost API URL in production!`

3. **Requests correctos**:
   - Las llamadas API deberían ir a `https://backend.aceleralia.com/api/v1/...`
   - **NO** a `localhost:8000/api/v1/...`

### Si Persiste el Problema
1. Verifica que las variables estén marcadas como "Build Arguments" en Coolify
2. Revisa los logs de build en Coolify para confirmar que las variables se están pasando
3. Haz un hard refresh (Ctrl+F5) en el navegador para limpiar cache

## Compatibilidad
- ✅ **Backward compatible**: Sigue funcionando con `VITE_API_URL` si existe
- ✅ **Forward compatible**: Usa `VITE_API_BASE_URL` como preferencia
- ✅ **Development friendly**: Fallback a localhost para desarrollo local
