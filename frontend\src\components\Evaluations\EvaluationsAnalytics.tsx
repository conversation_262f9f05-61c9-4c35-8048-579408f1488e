import React, { useMemo } from 'react';
import {
  LineChart,
  Line,
  AreaChart,
  Area,
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  <PERSON>atter<PERSON>hart,
  <PERSON>atter,
  Cell
} from 'recharts';
import { useEvaluacionStats } from '../../hooks/useEvaluations';
import { useRealtimeEvaluationsSubscription } from '../../hooks/useRealtimeEvaluations';

export const EvaluationsAnalytics: React.FC = () => {
  // Activar suscripciones en tiempo real
  useRealtimeEvaluationsSubscription();

  const { stats, isLoading, isError, error } = useEvaluacionStats();

  // Preparar datos para análisis avanzado
  const trendData = useMemo(() => {
    if (!stats?.tendencia_puntuacion) return [];
    
    return stats.tendencia_puntuacion.map(item => ({
      ...item,
      fecha_formateada: new Date(item.fecha).toLocaleDateString('es-ES', { 
        month: 'short', 
        day: 'numeric' 
      }),
      promedio_movil: item.promedio_puntuacion // Podríamos calcular media móvil aquí
    }));
  }, [stats?.tendencia_puntuacion]);

  const agentPerformanceData = useMemo(() => {
    if (!stats?.evaluaciones_por_agente) return [];
    
    return Object.entries(stats.evaluaciones_por_agente)
      .map(([agente, count]) => ({
        agente: agente.length > 15 ? `${agente.substring(0, 15)}...` : agente,
        agente_completo: agente,
        evaluaciones: Number(count),
        // Simular datos adicionales para análisis
        promedio_puntuacion: 6 + Math.random() * 4, // Entre 6-10
        mejoras_aplicadas: Math.floor(Math.random() * Number(count))
      }))
      .sort((a, b) => b.evaluaciones - a.evaluaciones)
      .slice(0, 8);
  }, [stats?.evaluaciones_por_agente]);

  const distributionData = useMemo(() => {
    // Simular distribución de puntuaciones
    const ranges = [
      { rango: '0-2', count: 2, color: '#EF4444' },
      { rango: '2-4', count: 5, color: '#F97316' },
      { rango: '4-6', count: 8, color: '#EAB308' },
      { rango: '6-8', count: 12, color: '#22C55E' },
      { rango: '8-10', count: 7, color: '#10B981' }
    ];
    return ranges;
  }, []);

  const correlationData = useMemo(() => {
    // Simular datos de correlación entre evaluaciones y mejoras
    return agentPerformanceData.map(agent => ({
      evaluaciones: agent.evaluaciones,
      promedio_puntuacion: agent.promedio_puntuacion,
      mejoras: agent.mejoras_aplicadas,
      agente: agent.agente_completo
    }));
  }, [agentPerformanceData]);

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-lg">Cargando análisis avanzado...</div>
      </div>
    );
  }

  if (isError) {
    return (
      <div className="bg-red-50 border border-red-200 rounded-lg p-4">
        <div className="text-red-800">Error al cargar análisis: {error?.message}</div>
      </div>
    );
  }

  return (
    <div className="space-y-8">
      <div>
        <h2 className="text-2xl font-bold text-gray-900 mb-4">Análisis Avanzado</h2>
        <p className="text-gray-600">
          Métricas detalladas y tendencias de rendimiento de agentes IA
        </p>
      </div>

      {/* Tendencias Temporales */}
      <div className="bg-white p-6 rounded-lg shadow-sm border">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">
          Tendencia de Puntuaciones en el Tiempo
        </h3>
        {trendData.length > 0 ? (
          <div className="h-80">
            <ResponsiveContainer width="100%" height="100%">
              <AreaChart data={trendData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis 
                  dataKey="fecha_formateada" 
                  fontSize={12}
                />
                <YAxis 
                  domain={[0, 10]}
                  fontSize={12}
                />
                <Tooltip 
                  formatter={(value, name) => [`${Number(value).toFixed(2)}`, 'Puntuación Promedio']}
                  labelFormatter={(label) => `Fecha: ${label}`}
                />
                <Area
                  type="monotone"
                  dataKey="promedio_puntuacion"
                  stroke="#3B82F6"
                  fill="#3B82F6"
                  fillOpacity={0.3}
                  strokeWidth={2}
                />
              </AreaChart>
            </ResponsiveContainer>
          </div>
        ) : (
          <div className="h-80 flex items-center justify-center text-gray-500">
            No hay datos de tendencia disponibles
          </div>
        )}
      </div>

      {/* Performance vs Mejoras */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* Rendimiento por Agente */}
        <div className="bg-white p-6 rounded-lg shadow-sm border">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">
            Rendimiento por Agente
          </h3>
          {agentPerformanceData.length > 0 ? (
            <div className="h-64">
              <ResponsiveContainer width="100%" height="100%">
                <BarChart data={agentPerformanceData} layout="horizontal">
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis type="number" fontSize={12} />
                  <YAxis 
                    type="category" 
                    dataKey="agente" 
                    width={100}
                    fontSize={10}
                  />
                  <Tooltip 
                    formatter={(value, name) => [
                      name === 'promedio_puntuacion' ? `${Number(value).toFixed(1)}/10` : value,
                      name === 'promedio_puntuacion' ? 'Puntuación Promedio' : 'Evaluaciones'
                    ]}
                    labelFormatter={(label) => {
                      const agent = agentPerformanceData.find(a => a.agente === label);
                      return `Agente: ${agent?.agente_completo || label}`;
                    }}
                  />
                  <Bar dataKey="evaluaciones" fill="#3B82F6" radius={[0, 4, 4, 0]} />
                </BarChart>
              </ResponsiveContainer>
            </div>
          ) : (
            <div className="h-64 flex items-center justify-center text-gray-500">
              No hay datos de rendimiento disponibles
            </div>
          )}
        </div>

        {/* Distribución de Puntuaciones */}
        <div className="bg-white p-6 rounded-lg shadow-sm border">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">
            Distribución de Puntuaciones
          </h3>
          <div className="h-64">
            <ResponsiveContainer width="100%" height="100%">
              <BarChart data={distributionData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="rango" fontSize={12} />
                <YAxis fontSize={12} />
                <Tooltip 
                  formatter={(value) => [`${value} evaluaciones`, 'Cantidad']}
                  labelFormatter={(label) => `Rango: ${label} puntos`}
                />
                <Bar dataKey="count" radius={[4, 4, 0, 0]}>
                  {distributionData.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={entry.color} />
                  ))}
                </Bar>
              </BarChart>
            </ResponsiveContainer>
          </div>
        </div>
      </div>

      {/* Correlación Evaluaciones vs Mejoras */}
      <div className="bg-white p-6 rounded-lg shadow-sm border">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">
          Correlación: Evaluaciones vs Puntuación Promedio
        </h3>
        {correlationData.length > 0 ? (
          <div className="h-80">
            <ResponsiveContainer width="100%" height="100%">
              <ScatterChart data={correlationData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis 
                  dataKey="evaluaciones" 
                  name="Evaluaciones"
                  fontSize={12}
                />
                <YAxis 
                  dataKey="promedio_puntuacion" 
                  name="Puntuación Promedio"
                  domain={[0, 10]}
                  fontSize={12}
                />
                <Tooltip 
                  formatter={(value, name) => [
                    name === 'promedio_puntuacion' ? `${Number(value).toFixed(1)}/10` : value,
                    name === 'promedio_puntuacion' ? 'Puntuación Promedio' : 'Evaluaciones'
                  ]}
                  labelFormatter={(label, payload) => {
                    if (payload && payload[0]) {
                      return `Agente: ${payload[0].payload.agente}`;
                    }
                    return '';
                  }}
                />
                <Scatter 
                  dataKey="promedio_puntuacion" 
                  fill="#8B5CF6"
                  r={6}
                />
              </ScatterChart>
            </ResponsiveContainer>
          </div>
        ) : (
          <div className="h-80 flex items-center justify-center text-gray-500">
            No hay datos de correlación disponibles
          </div>
        )}
      </div>

      {/* Métricas Clave */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div className="bg-gradient-to-r from-blue-500 to-blue-600 p-6 rounded-lg text-white">
          <div className="text-2xl font-bold">
            {stats?.total_evaluaciones || 0}
          </div>
          <div className="text-blue-100">Total Evaluaciones</div>
        </div>
        
        <div className="bg-gradient-to-r from-green-500 to-green-600 p-6 rounded-lg text-white">
          <div className="text-2xl font-bold">
            {stats?.promedio_puntuacion ? Number(stats.promedio_puntuacion).toFixed(1) : '0.0'}
          </div>
          <div className="text-green-100">Puntuación Promedio</div>
        </div>
        
        <div className="bg-gradient-to-r from-purple-500 to-purple-600 p-6 rounded-lg text-white">
          <div className="text-2xl font-bold">
            {Object.keys(stats?.evaluaciones_por_agente || {}).length}
          </div>
          <div className="text-purple-100">Agentes Evaluados</div>
        </div>
        
        <div className="bg-gradient-to-r from-orange-500 to-orange-600 p-6 rounded-lg text-white">
          <div className="text-2xl font-bold">
            {Object.values(stats?.evaluaciones_por_estado || {}).reduce((a, b) => Number(a) + Number(b), 0)}
          </div>
          <div className="text-orange-100">Estados Activos</div>
        </div>
      </div>
    </div>
  );
};
