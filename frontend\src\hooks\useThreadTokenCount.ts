import { useQuery } from '@tanstack/react-query';
import { useAuth } from './useAuth';
import { API_BASE_URL } from '../config/api';

interface TokenCountResponse {
  thread_id: number;
  token_count: number;
}

const fetchThreadTokenCount = async (threadId: number, accessToken: string): Promise<TokenCountResponse> => {
  const response = await fetch(`${API_BASE_URL}/threads/${threadId}/token_count`, {
    headers: {
      'Authorization': `Bearer ${accessToken}`,
    },
  });

  if (!response.ok) {
    throw new Error('Failed to fetch token count');
  }

  return response.json();
};

export const useThreadTokenCount = (threadId: number | null) => {
  const { session } = useAuth();
  const accessToken = session?.access_token;

  return useQuery<TokenCountResponse, Error>({
    queryKey: ['threadTokenCount', threadId],
    queryFn: () => {
      if (!threadId || !accessToken) {
        return Promise.resolve({ thread_id: threadId || 0, token_count: 0 });
      }
      return fetchThreadTokenCount(threadId, accessToken);
    },
    enabled: !!threadId && !!accessToken,
    refetchOnWindowFocus: false, // No need to refetch this constantly
    staleTime: 1000 * 60 * 5, // 5 minutes
  });
};