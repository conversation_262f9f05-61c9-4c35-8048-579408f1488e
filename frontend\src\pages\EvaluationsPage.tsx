import React, { useState } from 'react';
import { EvaluationsDashboard } from '../components/Evaluations/EvaluationsDashboard';

export const EvaluationsPage: React.FC = () => {
  const [activeTab, setActiveTab] = useState<string>('dashboard');

  const tabs = [
    { id: 'dashboard', label: 'Dashboard', icon: '📊' },
    { id: 'test', label: 'Test', icon: '🧪' }
  ];

  const renderTabContent = () => {
    switch (activeTab) {
      case 'dashboard':
        return <EvaluationsDashboard />;
      case 'test':
        return (
          <div className="bg-white rounded-lg shadow p-6">
            <h2 className="text-lg font-semibold mb-4">Test Tab</h2>
            <p>Esta es una pestaña de prueba para verificar la navegación.</p>
          </div>
        );
      default:
        return <EvaluationsDashboard />;
    }
  };

  return (
    <div className="flex flex-col h-full bg-gray-50">
      <div className="bg-white border-b border-gray-200 px-6 py-4">
        <h1 className="text-2xl font-bold text-gray-900">
          Evaluaciones de Agentes IA
        </h1>
        <p className="text-sm text-gray-600 mt-1">
          Gestión y análisis de evaluaciones de rendimiento
        </p>
      </div>

      {/* Navigation Tabs */}
      <div className="bg-white border-b border-gray-200 px-6">
        <nav className="flex space-x-8" aria-label="Tabs">
          {tabs.map((tab) => {
            const isActive = activeTab === tab.id;

            return (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`
                  group inline-flex items-center py-4 px-1 border-b-2 font-medium text-sm
                  ${isActive
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  }
                `}
                aria-current={isActive ? 'page' : undefined}
              >
                <span className="mr-2">{tab.icon}</span>
                {tab.label}
              </button>
            );
          })}
        </nav>
      </div>

      {/* Tab Content */}
      <div className="flex-1 p-6">
        {renderTabContent()}
      </div>
    </div>
  );
};
