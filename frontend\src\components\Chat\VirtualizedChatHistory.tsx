import React, { useRef, useEffect } from 'react';
import MessageBubble from './MessageBubble';
import { useChat } from '../../hooks/useChat';

interface VirtualizedChatHistoryProps {
  className?: string;
}

const VirtualizedChatHistory: React.FC<VirtualizedChatHistoryProps> = ({ className }) => {
  const { messages, isProcessingMessage, isLoadingHistory, currentThreadId } = useChat();
  const containerRef = useRef<HTMLDivElement>(null);

  // Scroll al final cuando se carga un nuevo thread (solo una vez)
  useEffect(() => {
    if (currentThreadId && messages.length > 0 && containerRef.current) {
      const container = containerRef.current;
      // Usar setTimeout para asegurar que el DOM se ha renderizado
      setTimeout(() => {
        if (container) {
          container.scrollTop = container.scrollHeight;
        }
      }, 100);
    }
  }, [currentThreadId, messages.length > 0]); // Solo cuando cambia el thread y hay mensajes

  // Función para ir al final (botón manual)
  const scrollToBottom = () => {
    if (containerRef.current) {
      containerRef.current.scrollTo({
        top: containerRef.current.scrollHeight,
        behavior: 'smooth'
      });
    }
  };

  return (
    <div className={`h-full flex flex-col ${className || ''}`}>
      {/* Loading indicator */}
      {isLoadingHistory && (
        <div className="flex justify-center items-center min-h-[200px]">
          <div className="flex items-center space-x-3">
            <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-indigo-600"></div>
            <p className="text-gray-500 italic">Cargando historial...</p>
          </div>
        </div>
      )}

      {/* Empty state */}
      {!isLoadingHistory && messages.length === 0 && (
        <div className="flex flex-col justify-center items-center min-h-[400px] text-center">
          <div className="bg-white rounded-2xl p-8 shadow-sm border border-gray-100 max-w-md">
            <div className="text-6xl mb-4">💬</div>
            <h3 className="text-lg font-medium text-gray-800 mb-2">¡Comienza una conversación!</h3>
            <p className="text-gray-500 text-sm">Selecciona un agente y envía tu primer mensaje para comenzar.</p>
          </div>
        </div>
      )}

      {/* Messages container - SIMPLIFICADO */}
      {!isLoadingHistory && messages.length > 0 && (
        <>
          {/* Info bar for large chats */}
          {messages.length > 100 && (
            <div className="flex justify-between items-center px-4 py-2 bg-blue-50 border-b border-blue-200 text-sm">
              <span className="text-blue-700">
                Chat largo: {messages.length} mensajes
              </span>
              <button
                onClick={scrollToBottom}
                className="text-blue-600 hover:text-blue-800 underline"
              >
                Ir al final ↓
              </button>
            </div>
          )}

          {/* Simple scroll container - SIN VIRTUALIZACIÓN */}
          <div
            ref={containerRef}
            className="flex-1 overflow-y-auto bg-gradient-to-b from-gray-50 to-white"
          >
            <div className="p-6 sm:p-8 space-y-8">
              {messages.map((msg, index) => (
                <MessageBubble
                  key={`${msg.thread_id}-${msg.message_id}-${index}-${msg.created_at}`}
                  message={msg}
                />
              ))}
            </div>

            {/* Processing indicator */}
            {isProcessingMessage && (
              <div className="flex justify-start mb-4 px-6">
                <div className="w-full max-w-4xl px-4 py-4 rounded-2xl bg-gradient-to-r from-indigo-50 to-purple-50 text-gray-700 border border-indigo-100 shadow-lg">
                  <div className="flex items-center space-x-3">
                    <div className="flex space-x-1">
                      <div className="w-3 h-3 bg-indigo-400 rounded-full animate-bounce"></div>
                      <div className="w-3 h-3 bg-indigo-400 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
                      <div className="w-3 h-3 bg-indigo-400 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
                    </div>
                    <span className="text-sm font-medium">El agente está pensando...</span>
                    <div className="ml-auto">
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-indigo-600"></div>
                    </div>
                  </div>
                </div>
              </div>
            )}
          </div>
        </>
      )}
    </div>
  );
};

export default VirtualizedChatHistory;
