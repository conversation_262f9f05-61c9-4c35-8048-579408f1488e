import React from 'react';
import { format } from 'date-fns';
import { es } from 'date-fns/locale';
import {
  Eye,
  CheckCircle,
  XCircle,
  Clock,
  Settings,
  ExternalLink,
  MoreHorizontal,
  FileText
} from 'lucide-react';
import { Button } from '../UI/button';
import { Badge } from '../UI/badge';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '../UI/table';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '../UI/dropdown-menu';
import { Checkbox } from '../UI/checkbox';
import {
  MejoraAgente,
  EstadoMejora
} from '../../types/evaluaciones';

interface ImprovementsTableProps {
  mejoras: MejoraAgente[];
  selectedIds: string[];
  onToggleSelection: (id: string) => void;
  onSelectAll: (checked: boolean) => void;
  onClearSelection: () => void;
  onViewDetails: (id: string) => void;
  onViewPromptComparison: (id: string) => void;
  onApply: (id: string) => void;
  onDiscard: (id: string) => void;
  isLoading?: boolean;
}

const getEstadoBadge = (estado: typeof EstadoMejora[keyof typeof EstadoMejora]) => {
  const variants: Record<string, {
    variant: 'secondary' | 'default' | 'destructive';
    icon: any;
    label: string;
  }> = {
    [EstadoMejora.PENDIENTE]: {
      variant: 'secondary' as const,
      icon: Clock,
      label: 'Pendiente'
    },
    [EstadoMejora.APLICADA]: {
      variant: 'default' as const,
      icon: CheckCircle,
      label: 'Aplicada'
    },
    [EstadoMejora.DESCARTADA]: {
      variant: 'destructive' as const,
      icon: XCircle,
      label: 'Descartada'
    },
  };

  const config = variants[estado as string];
  const Icon = config.icon;

  return (
    <Badge variant={config.variant} className="flex items-center gap-1">
      <Icon className="h-3 w-3" />
      {config.label}
    </Badge>
  );
};

const getPrioridadColor = (prioridad: number) => {
  if (prioridad >= 8) return 'text-red-600 font-bold';
  if (prioridad >= 6) return 'text-orange-600 font-semibold';
  if (prioridad >= 4) return 'text-yellow-600';
  return 'text-gray-600';
};

const hasPromptChanges = (mejora: MejoraAgente) => {
  return !!(
    mejora.user_prompt_plantilla ||
    mejora.user_prompt_mejorado ||
    mejora.system_prompt_plantilla ||
    mejora.system_prompt_mejorado
  );
};

export const ImprovementsTable: React.FC<ImprovementsTableProps> = ({
  mejoras,
  selectedIds,
  onToggleSelection,
  onSelectAll,
  onClearSelection,
  onViewDetails,
  onViewPromptComparison,
  onApply,
  onDiscard,
  isLoading = false,
}) => {
  const allSelected = mejoras.length > 0 && selectedIds.length === mejoras.length;
  const someSelected = selectedIds.length > 0 && selectedIds.length < mejoras.length;

  if (isLoading) {
    return (
      <div className="space-y-4">
        {[...Array(5)].map((_, i) => (
          <div key={i} className="animate-pulse">
            <div className="h-16 bg-gray-200 rounded"></div>
          </div>
        ))}
      </div>
    );
  }

  if (mejoras.length === 0) {
    return (
      <div className="text-center py-12">
        <Settings className="h-12 w-12 text-gray-400 mx-auto mb-4" />
        <h3 className="text-lg font-medium text-gray-900 mb-2">
          No hay mejoras disponibles
        </h3>
        <p className="text-gray-500">
          Las mejoras aparecerán aquí cuando se generen desde las evaluaciones.
        </p>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {/* Selection Controls */}
      {selectedIds.length > 0 && (
        <div className="flex items-center justify-between p-4 bg-blue-50 rounded-lg">
          <span className="text-sm text-blue-700">
            {selectedIds.length} mejora{selectedIds.length !== 1 ? 's' : ''} seleccionada{selectedIds.length !== 1 ? 's' : ''}
          </span>
          <div className="flex gap-2">
            <Button size="sm" variant="outline" onClick={onClearSelection}>
              Limpiar selección
            </Button>
          </div>
        </div>
      )}

      {/* Table */}
      <div className="border rounded-lg">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead className="w-12">
                <Checkbox
                  checked={allSelected}
                  ref={(el) => {
                    if (el) el.indeterminate = someSelected;
                  }}
                  onCheckedChange={(checked) => onSelectAll(!!checked)}
                />
              </TableHead>
              <TableHead>Agente</TableHead>
              <TableHead>Tipo</TableHead>
              <TableHead>Prioridad</TableHead>
              <TableHead>Estado</TableHead>
              <TableHead>Prompts</TableHead>
              <TableHead>Fecha</TableHead>
              <TableHead className="w-12"></TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {mejoras.map((mejora) => (
              <TableRow key={mejora.id} className="hover:bg-gray-50">
                <TableCell>
                  <Checkbox
                    checked={selectedIds.includes(mejora.id)}
                    onCheckedChange={() => onToggleSelection(mejora.id)}
                  />
                </TableCell>
                
                <TableCell>
                  <div>
                    <div className="font-medium">
                      {mejora.nombre_agente_amigable}
                    </div>
                    <div className="text-sm text-gray-500 font-mono">
                      {mejora.agente_id}
                    </div>
                  </div>
                </TableCell>
                
                <TableCell>
                  <Badge variant="outline">
                    {mejora.tipo_mejora}
                  </Badge>
                </TableCell>
                
                <TableCell>
                  <span className={`font-medium ${getPrioridadColor(mejora.prioridad || 0)}`}>
                    {mejora.prioridad}/10
                  </span>
                </TableCell>
                
                <TableCell>
                  {getEstadoBadge(mejora.estado)}
                </TableCell>

                <TableCell>
                  {hasPromptChanges(mejora) ? (
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => onViewPromptComparison(mejora.id)}
                      className="h-8 px-2"
                    >
                      <FileText className="h-3 w-3 mr-1" />
                      Ver cambios
                    </Button>
                  ) : (
                    <span className="text-xs text-gray-400">Sin cambios</span>
                  )}
                </TableCell>

                <TableCell className="text-sm text-gray-500">
                  {format(new Date(mejora.created_at), 'dd/MM/yyyy', { locale: es })}
                </TableCell>
                
                <TableCell>
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                        <MoreHorizontal className="h-4 w-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuItem onClick={() => onViewDetails(mejora.id)}>
                        <Eye className="h-4 w-4 mr-2" />
                        Ver detalles
                      </DropdownMenuItem>

                      {hasPromptChanges(mejora) && (
                        <DropdownMenuItem onClick={() => onViewPromptComparison(mejora.id)}>
                          <FileText className="h-4 w-4 mr-2" />
                          Comparar prompts
                        </DropdownMenuItem>
                      )}

                      {mejora.estado === EstadoMejora.PENDIENTE && (
                        <>
                          <DropdownMenuItem onClick={() => onApply(mejora.id)}>
                            <CheckCircle className="h-4 w-4 mr-2" />
                            Aplicar mejora
                          </DropdownMenuItem>
                          <DropdownMenuItem 
                            onClick={() => onDiscard(mejora.id)}
                            className="text-red-600"
                          >
                            <XCircle className="h-4 w-4 mr-2" />
                            Descartar
                          </DropdownMenuItem>
                        </>
                      )}
                      
                      {mejora.n8n_webhook_url && (
                        <DropdownMenuItem asChild>
                          <a 
                            href={mejora.n8n_webhook_url} 
                            target="_blank" 
                            rel="noopener noreferrer"
                            className="flex items-center"
                          >
                            <ExternalLink className="h-4 w-4 mr-2" />
                            Ver en n8n
                          </a>
                        </DropdownMenuItem>
                      )}
                    </DropdownMenuContent>
                  </DropdownMenu>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>
    </div>
  );
};
