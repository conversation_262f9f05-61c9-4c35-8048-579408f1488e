import React, { useState, useEffect, ReactNode, useCallback, useMemo } from 'react';
import { RealtimePostgresChangesPayload } from '@supabase/supabase-js';
import { supabase } from '../services/supabaseClient';
import { ChatContext, ChatContextProps, ChatMessage } from '../contexts/ChatContext';
import { useAuth } from '../hooks/useAuth';
import { useThreadHistory } from '../hooks/useThreadHistory';
import { ThreadMetadata } from '../types/chat';
import { API_BASE_URL } from '../config/api';
import { AgentDetails } from '../components/Chat/AgentSelector';
import { estimateTokensCached } from '../utils/tokenEstimator';
import useDebounce from '../hooks/useDebounce';

interface ChatProviderProps {
  children: ReactNode;
}

interface RawChatMessage {
  thread_id: number | string | bigint;
  content: string | null;
  type: string | null;
  from: 'User' | 'Agent' | null;
  message_id: number | null;
  agent_id: string | null;
  user_id: string | null;
  created_at: string;
  id?: string;
  input_token_cost?: number | null;
  output_token_cost?: number | null;
}

export const ChatProvider: React.FC<ChatProviderProps> = ({ children }) => {
  const { getToken } = useAuth();
  const [currentThreadId, setCurrentThreadId] = useState<number | null>(null);
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [selectedAgentId, setSelectedAgentId] = useState<string | null>(null);
  const [isProcessingMessage, setIsProcessingMessage] = useState<boolean>(false);
  const [isContextPanelOpen, setIsContextPanelOpen] = useState<boolean>(false);
  const [isAgentSelectionRequired, setIsAgentSelectionRequired] = useState<boolean>(false);
  const [currentThreadTitle, setCurrentThreadTitle] = useState<string | null>(null);
  const [isLoadingTitle, setIsLoadingTitle] = useState<boolean>(false);
  const [chatInputText, setChatInputText] = useState('');
  const [selectedAgentDetails, setSelectedAgentDetails] = useState<AgentDetails | null>(null);
  const [totalTokens, setTotalTokens] = useState(0);
  const [totalCost, setTotalCost] = useState(0);
  const [selectedModelName, setSelectedModelName] = useState<string | null>(null);
  const { data: historyData, isLoading: isLoadingHistory } = useThreadHistory(currentThreadId);

  const handleSetSelectedAgentId = useCallback((agentId: string | null) => {
    setSelectedAgentId(agentId);
    if (agentId) setIsAgentSelectionRequired(false);
  }, []);

  const addMessage = useCallback((message: ChatMessage) => {
    setMessages((prev) => [...prev, message]);
  }, []);

  const clearMessages = useCallback(() => setMessages([]), []);
  const startProcessing = useCallback(() => setIsProcessingMessage(true), []);
  const stopProcessing = useCallback(() => setIsProcessingMessage(false), []);
  const toggleContextPanel = useCallback(() => setIsContextPanelOpen((p) => !p), []);

  const startNewConversation = useCallback(() => {
    setCurrentThreadId(null);
    setMessages([]);
    setIsProcessingMessage(false);
    setSelectedAgentId(null);
    setSelectedAgentDetails(null);
    setChatInputText('');
    setSelectedModelName(null);
    setIsAgentSelectionRequired(true);
    setCurrentThreadTitle(null);
  }, []);

  const fetchThreadMetadata = useCallback(async (threadId: number) => {
    const token = await getToken();
    if (!token) return;
    try {
      setIsLoadingTitle(true);
      const response = await fetch(`${API_BASE_URL}/threads/${threadId}/metadata`, { headers: { 'Authorization': `Bearer ${token}` } });
      if (response.ok) {
        const metadata: ThreadMetadata = await response.json();
        setCurrentThreadTitle(metadata.titulo);
      } else {
        setCurrentThreadTitle('Chat sin título');
      }
    } catch {
      setCurrentThreadTitle('Chat sin título');
    } finally {
      setIsLoadingTitle(false);
    }
  }, [getToken]);

  const updateThreadTitle = useCallback(async (newTitle: string) => {
    const token = await getToken();
    if (!currentThreadId || !token) throw new Error('Authentication or thread ID missing');
    setIsLoadingTitle(true);
    try {
      const response = await fetch(`${API_BASE_URL}/threads/${currentThreadId}/metadata`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json', 'Authorization': `Bearer ${token}` },
        body: JSON.stringify({ titulo: newTitle }),
      });
      if (!response.ok) throw new Error('Failed to update title');
      const updatedMetadata: ThreadMetadata = await response.json();
      setCurrentThreadTitle(updatedMetadata.titulo);
    } finally {
      setIsLoadingTitle(false);
    }
  }, [currentThreadId, getToken]);

  useEffect(() => {
    if (currentThreadId) fetchThreadMetadata(currentThreadId);
    else setCurrentThreadTitle(null);
  }, [currentThreadId, fetchThreadMetadata]);

  useEffect(() => {
    const fetchAgentDetails = async () => {
      const token = await getToken();
      if (!selectedAgentId || !token) {
        setSelectedAgentDetails(null);
        return;
      }
      try {
        const response = await fetch(`${API_BASE_URL}/agents/${selectedAgentId}`, { headers: { 'Authorization': `Bearer ${token}` } });
        if (!response.ok) throw new Error('Failed to fetch agent details');
        const data: AgentDetails = await response.json();
        setSelectedAgentDetails(data);
      } catch {
        setSelectedAgentDetails(null);
      }
    };
    fetchAgentDetails();
  }, [selectedAgentId, getToken]);

  const debouncedChatInputText = useDebounce(chatInputText, 300);

  useEffect(() => {
    const calculateTokens = () => {
      if (!selectedAgentDetails) {
        setTotalTokens(0);
        return;
      }
      const getTextForTokenization = (message: ChatMessage): string => {
        if (!message.content) return '';
        try {
          JSON.parse(message.content);
          return message.content;
        } catch {
          return message.content;
        }
      };
      const flatMessages: ChatMessage[] = [];
      messages.forEach(msg => {
        flatMessages.push(msg);
        if (msg.intermediate_steps) flatMessages.push(...msg.intermediate_steps);
      });
      const historyText = flatMessages.map(getTextForTokenization).join('\n');
      const systemPromptText = selectedAgentDetails.system_prompt || '';
      const toolsText = selectedAgentDetails.tools ? JSON.stringify(selectedAgentDetails.tools) : '';
      const inputText = debouncedChatInputText;
      const combinedText = [systemPromptText, toolsText, historyText, inputText].join('\n\n');

      // Usar la nueva función de estimación de tokens optimizada
      const cacheKey = `thread_${currentThreadId}_agent_${selectedAgentDetails.id}`;
      setTotalTokens(estimateTokensCached(combinedText, cacheKey));
    };
    calculateTokens();
  }, [messages, selectedAgentDetails, debouncedChatInputText, currentThreadId]);

  useEffect(() => {
    const calculateCost = () => {
      if (!currentThreadId) {
        setTotalCost(0);
        return;
      }

      // Filtrar mensajes del thread actual y calcular coste total
      const currentThreadMessages = messages.filter(msg =>
        msg.thread_id && msg.thread_id.toString() === currentThreadId.toString()
      );

      // Incluir también los intermediate_steps en el cálculo
      const allMessages: ChatMessage[] = [];
      currentThreadMessages.forEach(msg => {
        allMessages.push(msg);
        if (msg.intermediate_steps) {
          allMessages.push(...msg.intermediate_steps);
        }
      });

      const currentTotal = allMessages.reduce((acc, msg) => {
        const inputCost = typeof msg.input_token_cost === 'number' ? msg.input_token_cost : 0;
        const outputCost = typeof msg.output_token_cost === 'number' ? msg.output_token_cost : 0;
        return acc + inputCost + outputCost;
      }, 0);

      setTotalCost(currentTotal);
    };
    calculateCost();
  }, [messages, currentThreadId]);

  const groupMessages = useCallback((allMessages: ChatMessage[]): ChatMessage[] => {
    const messageMap: { [key: number]: ChatMessage } = {};
    const finalGroupedMessages: ChatMessage[] = [];
    allMessages.forEach(msg => {
      const messageIdNum = typeof msg.message_id === 'string' ? parseInt(msg.message_id, 10) : msg.message_id;
      if (msg.from_sender === 'User' || msg.type === 'answer') {
        const messageToAdd = { ...msg, message_id: messageIdNum, intermediate_steps: msg.type === 'answer' ? [] : undefined };
        if (msg.type === 'answer' && messageIdNum) messageMap[messageIdNum] = messageToAdd;
        finalGroupedMessages.push(messageToAdd);
      } else if (msg.from_sender === 'Agent' && messageIdNum) {
        if (!messageMap[messageIdNum]) messageMap[messageIdNum] = { ...msg, message_id: messageIdNum, intermediate_steps: [] };
        messageMap[messageIdNum].intermediate_steps!.push(msg);
      } else {
        finalGroupedMessages.push({ ...msg, message_id: messageIdNum });
      }
    });
    const updatedFinalMessages = finalGroupedMessages.map(msg => (msg.type === 'answer' && msg.message_id && messageMap[msg.message_id]) ? messageMap[msg.message_id] : msg);
    updatedFinalMessages.sort((a, b) => new Date(a.created_at).getTime() - new Date(b.created_at).getTime());
    return updatedFinalMessages;
  }, []);

  useEffect(() => {
    if (historyData) setMessages(groupMessages(historyData));
    else if (!isLoadingHistory) setMessages([]);
  }, [historyData, isLoadingHistory, groupMessages]);

  useEffect(() => {
    if (messages.length > 0 && !selectedAgentId) {
      const lastAgentMessage = [...messages].reverse().find(msg => msg.from_sender === 'Agent' && msg.agent_id);
      if (lastAgentMessage?.agent_id) setSelectedAgentId(lastAgentMessage.agent_id);
    }
  }, [messages, selectedAgentId]);

  const handleRealtimeMessage = useCallback((payload: RealtimePostgresChangesPayload<RawChatMessage>) => {
    const messageData = payload.new as RawChatMessage;
    if (!messageData || !currentThreadId || messageData.thread_id?.toString() !== currentThreadId.toString()) return;
    
    const mappedMessage: ChatMessage = { ...messageData, id: messageData.id?.toString(), from_sender: messageData.from };
    
    if (mappedMessage.from_sender === 'Agent') {
      const messageIdNum = typeof mappedMessage.message_id === 'string' ? parseInt(mappedMessage.message_id, 10) : mappedMessage.message_id;
      const messageWithNumericId = { ...mappedMessage, message_id: messageIdNum };
      
      if (messageWithNumericId.type === 'answer') {
        setMessages(prev => [...prev, { ...messageWithNumericId, intermediate_steps: [] }]);
        stopProcessing();
      } else if (messageWithNumericId.message_id) {
        setMessages(prev => prev.map(msg => 
          (msg.type === 'answer' && msg.message_id === messageWithNumericId.message_id)
            ? { ...msg, intermediate_steps: [...(msg.intermediate_steps || []), messageWithNumericId].sort((a, b) => new Date(a.created_at).getTime() - new Date(b.created_at).getTime()) }
            : msg
        ));
      }
    }
  }, [currentThreadId, stopProcessing]);

  useEffect(() => {
    if (!currentThreadId) return;
    const channel = supabase.channel(`thread_changes:${currentThreadId}`);
    channel.on<RawChatMessage>('postgres_changes', { event: '*', schema: 'public', table: 'threads', filter: `thread_id=eq.${currentThreadId}` }, handleRealtimeMessage).subscribe();
    return () => { supabase.removeChannel(channel); };
  }, [currentThreadId, handleRealtimeMessage]);

  const contextValue: ChatContextProps = useMemo(() => ({
    currentThreadId, setCurrentThreadId, messages, selectedAgentId, setSelectedAgentId: handleSetSelectedAgentId,
    isProcessingMessage, isLoadingHistory, isContextPanelOpen, isAgentSelectionRequired, currentThreadTitle,
    isLoadingTitle, chatInputText, setChatInputText, selectedAgentDetails, setSelectedAgentDetails, totalTokens,
    totalCost, selectedModelName, setSelectedModelName, addMessage, clearMessages, startProcessing,
    stopProcessing, toggleContextPanel, startNewConversation, updateThreadTitle,
  }), [
    currentThreadId, messages, selectedAgentId, handleSetSelectedAgentId, isProcessingMessage, isLoadingHistory,
    isContextPanelOpen, isAgentSelectionRequired, currentThreadTitle, isLoadingTitle, chatInputText,
    selectedAgentDetails, totalTokens, totalCost, selectedModelName, addMessage, clearMessages,
    startProcessing, stopProcessing, toggleContextPanel, startNewConversation, updateThreadTitle,
  ]);

  return (
    <ChatContext.Provider value={contextValue}>
      {children}
    </ChatContext.Provider>
  );
};