import React, { useEffect, useRef, useMemo } from 'react';
import { FixedSizeList as List } from 'react-window';
import MessageBubble from './MessageBubble';
import { useChat } from '../../hooks/useChat';

// Altura estimada por mensaje (en píxeles)
const ITEM_HEIGHT = 200;
// Altura máxima del contenedor de chat
const MAX_CONTAINER_HEIGHT = 600;

interface MessageItemProps {
  index: number;
  style: React.CSSProperties;
  data: {
    messages: any[];
    isProcessingMessage: boolean;
  };
}

const MessageItem: React.FC<MessageItemProps> = ({ index, style, data }) => {
  const { messages } = data;
  const message = messages[index];

  if (!message) {
    return <div style={style} />;
  }

  return (
    <div style={style} className="px-6 sm:px-8">
      <MessageBubble message={message} />
    </div>
  );
};

const ChatHistory: React.FC = () => {
  const { messages, isProcessingMessage, isLoadingHistory } = useChat();
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const listRef = useRef<List>(null);

  // Calcular altura del contenedor basada en el número de mensajes
  const containerHeight = useMemo(() => {
    const totalHeight = messages.length * ITEM_HEIGHT;
    return Math.min(totalHeight, MAX_CONTAINER_HEIGHT);
  }, [messages.length]);

  // Auto-scroll al último mensaje cuando se añaden nuevos mensajes
  useEffect(() => {
    if (listRef.current && messages.length > 0) {
      listRef.current.scrollToItem(messages.length - 1, 'end');
    }
  }, [messages.length]);

  // Function to scroll to the bottom (para compatibilidad)
  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  };

  // Scroll to bottom whenever messages change (para compatibilidad)
  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  // Si hay pocos mensajes, renderizar normalmente sin virtualización
  if (messages.length <= 10) {
    return (
      <div className="h-full overflow-y-auto p-6 sm:p-8 space-y-8 bg-gradient-to-b from-gray-50 to-white">
        {/* Show loading indicator when fetching history */}
        {isLoadingHistory && (
          <div className="flex justify-center items-center min-h-[200px]">
            <div className="flex items-center space-x-3">
              <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-indigo-600"></div>
              <p className="text-gray-500 italic">Cargando historial...</p>
            </div>
          </div>
        )}

        {/* Show messages only when not loading history */}
        {!isLoadingHistory && messages.length === 0 && (
          <div className="flex flex-col justify-center items-center min-h-[400px] text-center">
            <div className="bg-white rounded-2xl p-8 shadow-sm border border-gray-100 max-w-md">
              <div className="text-6xl mb-4">💬</div>
              <h3 className="text-lg font-medium text-gray-800 mb-2">¡Comienza una conversación!</h3>
              <p className="text-gray-500 text-sm">Selecciona un agente y envía tu primer mensaje para comenzar.</p>
            </div>
          </div>
        )}

        {!isLoadingHistory && messages.map((msg, index) => (
          <MessageBubble
            key={`${msg.thread_id}-${msg.message_id}-${index}-${msg.created_at}`}
            message={msg}
          />
        ))}

        {/* Placeholder for "Thinking..." indicator */}
        {isProcessingMessage && !isLoadingHistory && (
          <div className="flex justify-start mb-4">
            <div className="w-full max-w-4xl px-4 py-4 rounded-2xl bg-gradient-to-r from-indigo-50 to-purple-50 text-gray-700 border border-indigo-100 shadow-lg">
              <div className="flex items-center space-x-3">
                <div className="flex space-x-1">
                  <div className="w-3 h-3 bg-indigo-400 rounded-full animate-bounce"></div>
                  <div className="w-3 h-3 bg-indigo-400 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
                  <div className="w-3 h-3 bg-indigo-400 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
                </div>
                <span className="text-sm font-medium">El agente está pensando...</span>
                <div className="ml-auto">
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-indigo-600"></div>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Empty div at the end to scroll to */}
        <div ref={messagesEndRef} />
      </div>
    );
  }

  // Para chats largos, usar virtualización
  return (
    <div className="h-full bg-gradient-to-b from-gray-50 to-white">
      {/* Show loading indicator when fetching history */}
      {isLoadingHistory && (
        <div className="flex justify-center items-center h-full">
          <div className="flex items-center space-x-3">
            <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-indigo-600"></div>
            <p className="text-gray-500 italic">Cargando historial...</p>
          </div>
        </div>
      )}

      {!isLoadingHistory && messages.length > 0 && (
        <List
          ref={listRef}
          height={containerHeight}
          width="100%"
          itemCount={messages.length}
          itemSize={ITEM_HEIGHT}
          itemData={{
            messages,
            isProcessingMessage,
          }}
          overscanCount={5} // Renderizar 5 elementos extra arriba y abajo para scroll suave
        >
          {MessageItem}
        </List>
      )}

      {/* Placeholder for "Thinking..." indicator */}
      {isProcessingMessage && !isLoadingHistory && (
        <div className="flex justify-start p-6 sm:p-8">
          <div className="w-full max-w-4xl px-4 py-4 rounded-2xl bg-gradient-to-r from-indigo-50 to-purple-50 text-gray-700 border border-indigo-100 shadow-lg">
            <div className="flex items-center space-x-3">
              <div className="flex space-x-1">
                <div className="w-3 h-3 bg-indigo-400 rounded-full animate-bounce"></div>
                <div className="w-3 h-3 bg-indigo-400 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
                <div className="w-3 h-3 bg-indigo-400 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
              </div>
              <span className="text-sm font-medium">El agente está pensando...</span>
              <div className="ml-auto">
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-indigo-600"></div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Empty div at the end to scroll to */}
      <div ref={messagesEndRef} />
    </div>
  );
};

export default ChatHistory;