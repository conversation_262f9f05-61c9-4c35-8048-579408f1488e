import { useEffect, useCallback } from 'react';
import { useQueryClient } from '@tanstack/react-query';
import { supabase } from '../services/supabaseClient';

/**
 * Hook para manejar actualizaciones en tiempo real de evaluaciones y mejoras
 * Utiliza Supabase Realtime para escuchar cambios en las tablas relevantes
 */
export const useRealtimeEvaluations = () => {
  const queryClient = useQueryClient();

  // Función para invalidar queries relacionadas con evaluaciones
  const invalidateEvaluationQueries = useCallback(() => {
    // Invalidar todas las queries relacionadas con evaluaciones
    queryClient.invalidateQueries({ queryKey: ['evaluaciones'] });
    queryClient.invalidateQueries({ queryKey: ['evaluacion-stats'] });
    queryClient.invalidateQueries({ queryKey: ['evaluacion'] });
    
    console.log('🔄 Queries de evaluaciones invalidadas por cambio en tiempo real');
  }, [queryClient]);

  // Función para invalidar queries relacionadas con mejoras
  const invalidateMejorasQueries = useCallback(() => {
    // Invalidar todas las queries relacionadas con mejoras
    queryClient.invalidateQueries({ queryKey: ['mejoras'] });
    queryClient.invalidateQueries({ queryKey: ['mejora'] });
    
    console.log('🔄 Queries de mejoras invalidadas por cambio en tiempo real');
  }, [queryClient]);

  useEffect(() => {
    console.log('🔌 Configurando suscripciones en tiempo real para evaluaciones...');

    // Suscripción a cambios en la tabla evaluaciones
    const evaluacionesSubscription = supabase
      .channel('evaluaciones-changes')
      .on(
        'postgres_changes',
        {
          event: '*', // Escuchar todos los eventos (INSERT, UPDATE, DELETE)
          schema: 'public',
          table: 'evaluaciones'
        },
        (payload) => {
          console.log('📊 Cambio detectado en evaluaciones:', payload);
          invalidateEvaluationQueries();
        }
      )
      .subscribe((status) => {
        console.log('📊 Estado suscripción evaluaciones:', status);
      });

    // Suscripción a cambios en la tabla mejoras_agentes
    const mejorasSubscription = supabase
      .channel('mejoras-changes')
      .on(
        'postgres_changes',
        {
          event: '*', // Escuchar todos los eventos (INSERT, UPDATE, DELETE)
          schema: 'public',
          table: 'mejoras_agentes'
        },
        (payload) => {
          console.log('🔧 Cambio detectado en mejoras_agentes:', payload);
          invalidateMejorasQueries();
          // También invalidar stats de evaluaciones porque pueden verse afectadas
          invalidateEvaluationQueries();
        }
      )
      .subscribe((status) => {
        console.log('🔧 Estado suscripción mejoras:', status);
      });

    // Cleanup function
    return () => {
      console.log('🔌 Desconectando suscripciones en tiempo real...');
      evaluacionesSubscription.unsubscribe();
      mejorasSubscription.unsubscribe();
    };
  }, [invalidateEvaluationQueries, invalidateMejorasQueries]);

  return {
    // Funciones para invalidar manualmente si es necesario
    invalidateEvaluationQueries,
    invalidateMejorasQueries
  };
};

/**
 * Hook simplificado para componentes que solo necesitan activar las suscripciones
 */
export const useRealtimeEvaluationsSubscription = () => {
  useRealtimeEvaluations();
};
