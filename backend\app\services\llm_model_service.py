import logging
from typing import List
from fastapi import HTTPException, status
from app.core.database import get_supabase_client
from app.models.llm_model import LLMModel

logger = logging.getLogger(__name__)

async def get_active_llm_models() -> List[LLMModel]:
    """
    Fetches all active LLM models from the database.
    """
    try:
        supabase = await get_supabase_client()
        logger.info("Fetching active LLM models")
        response = supabase.table("llm_models").select("*").eq("active", True).execute()
        
        if response.data:
            validated_models = [LLMModel.model_validate(model_data) for model_data in response.data]
            logger.info(f"Found {len(validated_models)} active LLM models.")
            return validated_models
        else:
            logger.info("No active LLM models found.")
            return []
    except Exception as e:
        logger.exception(f"Database error while fetching active LLM models: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"An error occurred while fetching active LLM models: {e}",
        )