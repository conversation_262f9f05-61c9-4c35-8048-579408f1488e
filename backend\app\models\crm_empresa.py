from pydantic import BaseModel, EmailStr, field_validator
from typing import Optional, Literal, List
from uuid import UUID
import datetime # Required for date
from app.constants.hallazgos import HallazgoTipoLiteral, is_valid_hallazgo_tipo

class EmpresaBase(BaseModel):
    nombre: str
    nif_cif: Optional[str] = None
    sector: Optional[str] = None
    descripcion: Optional[str] = None
    logo_url: Optional[str] = None
    direccion: Optional[str] = None
    direccion_fiscal: Optional[str] = None
    telefono: Optional[str] = None
    email_principal: Optional[EmailStr] = None
    website: Optional[str] = None
    tipo_empresa: Optional[str] = None # Consider ENUM if predefined list
    tipo_relacion: Optional[Literal['Cliente', 'Colaborador', 'Otro', 'Lead']] = None
    activo: bool = True
    info_adicional: Optional[str] = None

class EmpresaCreate(EmpresaBase):
    pass

class Empresa(EmpresaBase):
    id: UUID
    fecha_alta: Optional[datetime.date] = None # From DB schema, not in form doc initially
    created_at: datetime.datetime
    updated_at: datetime.datetime

    class Config:
        from_attributes = True

class EmpresaResponse(Empresa):
    pass

# General Tab Models
class DepartmentInfo(BaseModel):
    id: UUID
    nombre: str
    descripcion: Optional[str] = None

class ProjectInfo(BaseModel):
    id: UUID
    nombre: str
    descripcion: Optional[str] = None
    progreso: float = 0.0  # 0-100
    estado: Optional[str] = None
    fecha_inicio: Optional[datetime.date] = None
    fecha_fin_estimada: Optional[datetime.date] = None

class MeetingStats(BaseModel):
    total_reuniones: int = 0
    total_entrevistas: int = 0
    reuniones_vs_entrevistas_ratio: float = 0.0

class ProcessStats(BaseModel):
    total_procesos_clientes: int = 0
    total_tareas_clientes: int = 0

class FindingDistribution(BaseModel):
    tipo: str
    count: int
    percentage: float

class PeoplePanel(BaseModel):
    total_personas_activas: int = 0
    total_personas_entrevistadas: int = 0
    porcentaje_entrevistadas: float = 0.0

class EmpresaGeneralDetails(BaseModel):
    # Basic company info
    empresa: Empresa

    # Main information section
    departamentos: List[DepartmentInfo] = []
    proyectos_relacionados: List[ProjectInfo] = []
    total_trabajadores_activos: int = 0

    # Statistics panel
    estadisticas_reuniones: MeetingStats
    estadisticas_procesos: ProcessStats
    distribucion_hallazgos: List[FindingDistribution] = []
    panel_personas: PeoplePanel

# Hallazgos Tab Models
class HallazgoListItem(BaseModel):
    id: UUID
    titulo: Optional[str] = None
    tipo: Optional[HallazgoTipoLiteral] = None  # Strict validation for hallazgo types
    impacto: Optional[str] = None
    departamento_nombre: Optional[str] = None
    persona_nombre: Optional[str] = None
    estado: Optional[str] = None
    created_at: datetime.datetime

    @field_validator('tipo')
    @classmethod
    def validate_tipo(cls, v):
        if v is not None and not is_valid_hallazgo_tipo(v):
            raise ValueError(f'Invalid hallazgo tipo: {v}')
        return v

class HallazgoDetail(BaseModel):
    id: UUID
    titulo: Optional[str] = None
    tipo: Optional[HallazgoTipoLiteral] = None  # Strict validation for hallazgo types
    impacto: Optional[str] = None
    descripcion: Optional[str] = None
    posible_solucion: Optional[str] = None
    estado: Optional[str] = None
    departamento_nombre: Optional[str] = None
    persona_nombre: Optional[str] = None
    procesos_relacionados: List[str] = []  # Process names
    created_at: datetime.datetime
    updated_at: datetime.datetime

    @field_validator('tipo')
    @classmethod
    def validate_tipo(cls, v):
        if v is not None and not is_valid_hallazgo_tipo(v):
            raise ValueError(f'Invalid hallazgo tipo: {v}')
        return v

class HallazgoTypeStats(BaseModel):
    tipo: str
    count: int
    percentage: float

# Typed models for better type safety
class PersonaDisponible(BaseModel):
    id: UUID
    nombre_completo: str

class DepartamentoDisponible(BaseModel):
    id: UUID
    nombre: str

class HallazgoFilters(BaseModel):
    personas_disponibles: List[PersonaDisponible] = []
    departamentos_disponibles: List[DepartamentoDisponible] = []
    tipos_disponibles: List[str] = []
    impactos_disponibles: List[str] = []

class EmpresaHallazgosDetails(BaseModel):
    # Summary statistics
    total_hallazgos: int = 0
    distribucion_tipos: List[HallazgoTypeStats] = []

    # Main data
    hallazgos: List[HallazgoListItem] = []

    # Filter options
    filtros_disponibles: HallazgoFilters

# Reuniones Tab Models
class PersonaEstadoEntrevista(BaseModel):
    id: UUID
    nombre: str
    apellidos: Optional[str] = None
    cargo: Optional[str] = None
    entrevistado: bool = False
    total_reuniones_asistidas: int = 0

class ReunionListItem(BaseModel):
    id: UUID
    titulo: Optional[str] = None
    fecha_reunion: Optional[datetime.datetime] = None
    tipo: str  # "Reunión" or "Entrevista" based on entrevista boolean
    estado_procesamiento: Optional[str] = None
    personas_asistentes: List[str] = []  # Names of attendees from this company
    personas_asistentes_ids: List[UUID] = []  # IDs for filtering

class ReunionTimelineItem(BaseModel):
    id: UUID
    titulo: Optional[str] = None
    fecha_reunion: Optional[datetime.datetime] = None
    tipo: str  # "Reunión" or "Entrevista"
    resumen_breve: Optional[str] = None
    personas_asistentes: List[str] = []

class ReunionStats(BaseModel):
    total_reuniones: int = 0
    total_entrevistas: int = 0
    total_personas_entrevistadas: int = 0
    porcentaje_personas_entrevistadas: float = 0.0

class EmpresaReunionesDetails(BaseModel):
    reuniones_list: List[ReunionListItem] = []
    personas_empresa: List[PersonaEstadoEntrevista] = []
    timeline_items: List[ReunionTimelineItem] = []
    estadisticas: ReunionStats
    total_reuniones: int = 0
