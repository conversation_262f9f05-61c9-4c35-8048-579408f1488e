import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { useAuth } from '../hooks/useAuth'; // Import useAuth
import { useToastContext } from '../hooks/useToastContext';
import { TrashIcon, EyeIcon } from '@heroicons/react/24/outline';
import { API_BASE_URL } from '../config/api';

// Define a type for the reunion data expected from the API list endpoint
// This should align with the backend's ReunionResponse model,
// but might be a summary version for list display.
interface ReunionListItem {
  id: string; // UUID
  titulo?: string | null;
  fecha_reunion?: string | null; // ISO string
  estado_procesamiento?: string | null;
  created_at: string; // ISO string
  // Add other relevant fields for list display
}

const MeetingsListPage: React.FC = () => {
  const [meetings, setMeetings] = useState<ReunionListItem[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [deletingId, setDeletingId] = useState<string | null>(null);
  const { session } = useAuth();
  const { success, error: showError } = useToastContext();

  useEffect(() => {
    const fetchMeetings = async () => {
      setIsLoading(true);
      setError(null);
      if (!session?.access_token) {
        setError("No hay sesión de usuario activa o token no disponible.");
        setIsLoading(false);
        setMeetings([]); // Clear meetings if no session
        return;
      }

      try {
        const response = await fetch(`${API_BASE_URL}/reuniones`, {
          headers: {
            'Authorization': `Bearer ${session.access_token}`,
          },
        });
        if (!response.ok) {
          const errorData = await response.json().catch(() => ({ detail: `HTTP error ${response.status}` }));
          throw new Error(errorData.detail || `Error fetching meetings: ${response.statusText}`);
        }
        const data: ReunionListItem[] = await response.json();
        setMeetings(data);

      } catch (err) {
        setError(err instanceof Error ? err.message : 'Ocurrió un error desconocido.');
        console.error("Failed to fetch meetings:", err);
        setMeetings([]); // Clear meetings on error
      } finally {
        setIsLoading(false);
      }
    };

    fetchMeetings();
  }, [session]); // Dependency on session

  if (!session && !isLoading) { // If no session and not loading, show login prompt or similar
    return (
      <div className="p-6 text-center">
        <p>Por favor, <Link to="/login" className="text-indigo-600 hover:text-indigo-800">inicie sesión</Link> para ver sus reuniones.</p>
      </div>
    );
  }

  if (isLoading) {
    return <div className="p-6 text-center">Cargando reuniones...</div>;
  }

  if (error) {
    return <div className="p-6 text-center text-red-600">Error: {error}</div>;
  }

  return (
    <div className="p-6 bg-gray-50 min-h-screen">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-semibold text-gray-800">Mis Reuniones</h1>
        <Link
          to="/meetings/new" // Assuming a route for the MeetingProcessingForm
          className="px-4 py-2 text-sm font-medium text-white bg-indigo-600 border border-transparent rounded-md shadow-sm hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
        >
          + Nueva Reunión
        </Link>
      </div>

      {meetings.length === 0 ? (
        <p className="text-gray-600">No tienes reuniones grabadas todavía.</p>
      ) : (
        <div className="bg-white shadow-md rounded-lg overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Título</th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Fecha Reunión</th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Estado</th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Creada</th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Acciones</th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {meetings.map((meeting) => (
                <tr key={meeting.id}>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                    {meeting.titulo || 'Reunión sin título'}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {meeting.fecha_reunion ? new Date(meeting.fecha_reunion).toLocaleString() : 'N/A'}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                      meeting.estado_procesamiento === 'completado' ? 'bg-green-100 text-green-800' :
                      meeting.estado_procesamiento === 'pending_transcripcion' ? 'bg-yellow-100 text-yellow-800' :
                      meeting.estado_procesamiento === 'pending_asignacion_speakers' ? 'bg-blue-100 text-blue-800' :
                      meeting.estado_procesamiento === 'procesando_ia' ? 'bg-purple-100 text-purple-800' :
                      'bg-gray-100 text-gray-800'
                    }`}>
                      {meeting.estado_procesamiento || 'Desconocido'}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {new Date(meeting.created_at).toLocaleDateString()}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                    {meeting.estado_procesamiento === 'pending_asignacion_speakers' && (
                      <Link to={`/meetings/process/${meeting.id}`} className="text-blue-600 hover:text-blue-900">
                        Asignar Speakers
                      </Link>
                    )}
                    {(meeting.estado_procesamiento === 'pending_transcripcion' || meeting.estado_procesamiento === 'procesando_ia') && (
                      <Link to={`/meetings/process/${meeting.id}`} className="text-gray-600 hover:text-gray-900">
                        Ver Progreso
                      </Link>
                    )}
                    {(meeting.estado_procesamiento === 'completado' || meeting.estado_procesamiento?.startsWith('error')) && (
                       <Link
                         to={`/meetings/${meeting.id}`}
                         className="text-indigo-600 hover:text-indigo-900 flex items-center space-x-1"
                       >
                         <EyeIcon className="h-4 w-4" />
                         <span>Ver Detalles</span>
                       </Link>
                    )}
                    {/* Always show delete button, or conditionally based on status if preferred */}
                    <button
                      onClick={async () => {
                        if (window.confirm(`¿Estás seguro de que quieres eliminar la reunión "${meeting.titulo || 'Reunión sin título'}"? Esta acción no se puede deshacer.`)) {
                          if (!session?.access_token) {
                            showError("Error de autenticación", "Por favor, inicie sesión de nuevo.");
                            return;
                          }
                          setDeletingId(meeting.id);
                          try {
                            const response = await fetch(`${API_BASE_URL}/reuniones/${meeting.id}`, {
                              method: 'DELETE',
                              headers: { 'Authorization': `Bearer ${session.access_token}` },
                            });
                            if (!response.ok) {
                              const errorData = await response.json().catch(() => ({ detail: `HTTP error ${response.status}` }));
                              throw new Error(errorData.detail || `Error al eliminar reunión: ${response.statusText}`);
                            }
                            // Refresh list
                            setMeetings(prevMeetings => prevMeetings.filter(m => m.id !== meeting.id));
                            success("Reunión eliminada", "La reunión se ha eliminado correctamente.");
                          } catch (err) {
                            showError("Error al eliminar", err instanceof Error ? err.message : 'Error desconocido al eliminar.');
                            console.error("Failed to delete meeting:", err);
                          } finally {
                            setDeletingId(null);
                          }
                        }
                      }}
                      disabled={deletingId === meeting.id}
                      className={`${
                        deletingId === meeting.id
                          ? 'text-gray-400 cursor-not-allowed'
                          : 'text-red-600 hover:text-red-900'
                      } flex items-center space-x-1`}
                    >
                      {deletingId === meeting.id ? (
                        <>
                          <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-gray-400"></div>
                          <span>Eliminando...</span>
                        </>
                      ) : (
                        <>
                          <TrashIcon className="h-4 w-4" />
                          <span>Eliminar</span>
                        </>
                      )}
                    </button>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      )}
    </div>
  );
};

export default MeetingsListPage;
