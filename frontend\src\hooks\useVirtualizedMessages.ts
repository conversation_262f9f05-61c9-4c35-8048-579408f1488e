import { useState, useEffect, useMemo, useCallback } from 'react';
import { ChatMessage } from '../contexts/ChatContext';

interface VirtualizationConfig {
  estimatedItemHeight: number;
  bufferSize: number;
  containerHeight: number;
  scrollTop: number;
}

interface VirtualizedResult<T = ChatMessage> {
  visibleMessages: T[];
  totalHeight: number;
  offsetY: number;
  visibleRange: { start: number; end: number };
  isAtBottom: boolean;
}

export const useVirtualizedMessages = <T extends ChatMessage>(
  messages: T[],
  config: VirtualizationConfig
): VirtualizedResult<T> => {
  const { estimatedItemHeight, bufferSize, containerHeight, scrollTop } = config;
  
  // Calcular el rango visible
  const visibleRange = useMemo(() => {
    if (messages.length === 0) return { start: 0, end: 0 };

    const visibleCount = Math.ceil(containerHeight / estimatedItemHeight);
    const startIndex = Math.max(0, Math.floor(scrollTop / estimatedItemHeight) - bufferSize);
    const endIndex = Math.min(messages.length, startIndex + visibleCount + (bufferSize * 2));

    return { start: startIndex, end: endIndex };
  }, [messages.length, containerHeight, scrollTop, estimatedItemHeight, bufferSize]);

  // Mensajes visibles
  const visibleMessages = useMemo(() => {
    return messages.slice(visibleRange.start, visibleRange.end);
  }, [messages, visibleRange]);

  // Altura total
  const totalHeight = useMemo(() => {
    return messages.length * estimatedItemHeight;
  }, [messages.length, estimatedItemHeight]);

  // Offset Y para posicionamiento
  const offsetY = visibleRange.start * estimatedItemHeight;

  // Detectar si está en el bottom
  const isAtBottom = useMemo(() => {
    if (containerHeight === 0 || totalHeight === 0) return true;
    return (totalHeight - scrollTop - containerHeight) < 50;
  }, [scrollTop, containerHeight, totalHeight]);

  return {
    visibleMessages,
    totalHeight,
    offsetY,
    visibleRange,
    isAtBottom
  };
};

// Hook para manejar el scroll inteligente
export const useSmartScroll = (
  containerRef: React.RefObject<HTMLDivElement | null>,
  messagesLength: number,
  threadId: number | string | null
) => {
  const [scrollTop, setScrollTop] = useState(0);
  const [isAtBottom, setIsAtBottom] = useState(true);
  const [containerHeight, setContainerHeight] = useState(600);

  // Observar cambios de tamaño del contenedor
  useEffect(() => {
    const container = containerRef.current;
    if (!container) return;

    const resizeObserver = new ResizeObserver((entries) => {
      const entry = entries[0];
      if (entry) {
        setContainerHeight(entry.contentRect.height);
      }
    });

    resizeObserver.observe(container);
    return () => resizeObserver.disconnect();
  }, []);

  // Manejar scroll con mejor detección de bottom
  const handleScroll = useCallback((e: React.UIEvent<HTMLDivElement>) => {
    const target = e.target as HTMLDivElement;
    const { scrollTop: newScrollTop, scrollHeight, clientHeight } = target;

    setScrollTop(newScrollTop);

    // Detectar si está en el bottom con más precisión
    const threshold = 10; // Reducir threshold para mejor detección
    const atBottom = Math.abs(scrollHeight - newScrollTop - clientHeight) <= threshold;
    setIsAtBottom(atBottom);
  }, []);

  // Auto-scroll al bottom para nuevos mensajes (sin bounce)
  useEffect(() => {
    if (isAtBottom && containerRef.current) {
      const container = containerRef.current;
      // Usar requestAnimationFrame para scroll más suave
      requestAnimationFrame(() => {
        if (container) {
          container.scrollTop = container.scrollHeight;
        }
      });
    }
  }, [messagesLength, isAtBottom]);

  // Scroll al bottom cuando cambia el thread
  useEffect(() => {
    if (containerRef.current && threadId) {
      const container = containerRef.current;
      // Scroll inmediato sin animación para cambio de thread
      container.scrollTop = container.scrollHeight;
      setIsAtBottom(true);
    }
  }, [threadId]);

  // Función para scroll manual al bottom (suave)
  const scrollToBottom = useCallback(() => {
    if (containerRef.current) {
      const container = containerRef.current;
      container.scrollTo({
        top: container.scrollHeight,
        behavior: 'smooth'
      });
      setIsAtBottom(true);
    }
  }, []);

  return {
    scrollTop,
    containerHeight,
    isAtBottom,
    handleScroll,
    scrollToBottom
  };
};

// Tipo extendido para mensajes optimizados
interface OptimizedChatMessage extends ChatMessage {
  stableKey: string;
}

// Hook para optimización de rendimiento
export const useMessageOptimization = (messages: ChatMessage[]) => {
  // Memoizar mensajes para evitar re-renders innecesarios
  const optimizedMessages = useMemo((): OptimizedChatMessage[] => {
    return messages.map(msg => ({
      ...msg,
      // Crear una key estable para React
      stableKey: `${msg.thread_id}-${msg.message_id}-${msg.created_at}`
    }));
  }, [messages]);

  // Estadísticas para debugging
  const stats = useMemo(() => ({
    totalMessages: messages.length,
    isLargeChat: messages.length > 100,
    shouldUseVirtualization: messages.length > 50
  }), [messages.length]);

  return {
    optimizedMessages,
    stats
  };
};
