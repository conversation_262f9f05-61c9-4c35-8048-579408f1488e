// Meeting-related type definitions
export interface AudioSegmentInfo {
  startTime: number;
  duration: number;
  audioSrc: string;
}

export interface SpeakerAssignable {
  id: string;
  nombre: string;
  tipo: 'persona' | 'usuario';
  apellidos?: string | null;
  email?: string;
}

export interface AssociatedEntity {
  id: string;
  nombre: string;
  tipo: 'empresa' | 'persona';
  apellidos?: string | null;
  email?: string;
}

export interface MeetingState {
  // Core meeting data
  id: string | null;
  status: string | null;
  titulo: string | null;
  observacionesIniciales: string | null;
  fechaReunion: string | null;
  entrevista: boolean | null;
  video: boolean | null;

  // Transcription data
  rawTranscript: string | null;
  displayTranscript: string | null;
  identifiedSpeakers: string[];

  // Assignment data
  speakerAssignments: Record<string, SpeakerAssignable>;
  audioSegments: Map<string, AudioSegmentInfo>;

  // Association data
  associatedEntities: AssociatedEntity[];
  potentialSpeakers: SpeakerAssignable[];

  // UI state
  currentStep: number;
  isUploading: boolean;
  uploadProgress: number;

  // Connection state
  connectionStatus: 'SUBSCRIBED' | 'TIMED_OUT' | 'CLOSED' | 'CHANNEL_ERROR' | 'disconnected' | 'error' | 'polling'; // Updated to include Supabase states + our logical ones
  connectionError: string | null;
}

export type MeetingAction =
  | { type: 'RESET' }
  | { type: 'LOAD_MEETING'; payload: Partial<MeetingState> }
  | { type: 'SET_ID'; id: string }
  | { type: 'UPDATE_STATUS'; status: string }
  | { type: 'SET_ENTREVISTA'; entrevista: boolean }
  | { type: 'SET_VIDEO'; video: boolean }
  | { type: 'SET_TRANSCRIPT'; rawTranscript: string; displayTranscript: string }
  | { type: 'SET_SPEAKERS'; speakers: string[] }
  | { type: 'UPDATE_ASSIGNMENT'; speakerTag: string; assignment: SpeakerAssignable }
  | { type: 'SET_AUDIO_SEGMENTS'; segments: Map<string, AudioSegmentInfo> }
  | { type: 'ADD_ASSOCIATED_ENTITY'; entity: AssociatedEntity }
  | { type: 'REMOVE_ASSOCIATED_ENTITY'; id: string; tipo: AssociatedEntity['tipo'] }
  | { type: 'SET_POTENTIAL_SPEAKERS'; speakers: SpeakerAssignable[] }
  | { type: 'SET_STEP'; step: number }
  | { type: 'SET_UPLOAD_PROGRESS'; progress: number }
  | { type: 'SET_UPLOADING'; isUploading: boolean }
  | { type: 'SET_CONNECTION_STATUS'; status: MeetingState['connectionStatus'] }
  | { type: 'SET_CONNECTION_ERROR'; error: string | null };

// Realtime payload types
export interface ReunionRecord {
  id: string;
  estado_procesamiento?: string;
  transcripcion_raw?: string;
  titulo?: string;
  url_grabacion_publica?: string;
  [key: string]: unknown; // Allows for other properties from the DB row
}

// Type for the payload received from Supabase Broadcast when using realtime.broadcast_changes()
export interface SupabaseBroadcastChangesPayload {
  // schema: string; // e.g., 'public'
  // table: string;  // e.g., 'reuniones'
  // operation: 'INSERT' | 'UPDATE' | 'DELETE'; // TG_OP
  record?: ReunionRecord; // For INSERT, UPDATE (this is the NEW.*)
  old_record?: Partial<ReunionRecord>; // For UPDATE, DELETE (this is the OLD.*)
  // Supabase might add other metadata, allow for it:
  [key: string]: unknown;
}

// Backend response types
export interface BackendReunion {
  id: string;
  user_id: string;
  uploader_info?: { id: string; nombre?: string | null; email: string };
  titulo?: string | null;
  observaciones_iniciales?: string | null;
  fecha_reunion?: string | null;
  estado_procesamiento?: string;
  transcripcion_raw?: string;
  url_grabacion_publica?: string;
  entrevista?: boolean | null;
  video?: boolean | null;
  empresas_asociadas?: Array<{ id: string; nombre: string; [key: string]: unknown }>;
  personas_asociadas?: Array<{ id: string; nombre: string; apellidos?: string | null; [key: string]: unknown }>;

  [key: string]: unknown;
}

// Additional types for better type safety
export interface ReunionListItem {
  id: string;
  titulo?: string | null;
  estado_procesamiento?: string;
  fecha_reunion?: string | null;
  created_at: string;
  updated_at: string;
}

export interface MeetingFormData {
  titulo?: string;
  observaciones_iniciales?: string;
  fecha_reunion?: string;
  file?: FileList;
}

export interface SpeakerAssignmentData {
  speaker_tag: string;
  asignado_a_tipo: string;
  asignado_a_id: string;
  nombre_asignado: string;
}

// Error types
export interface MeetingError {
  code: string;
  message: string;
  details?: unknown;
}

// Connection status types
export type ConnectionStatus = 'SUBSCRIBED' | 'TIMED_OUT' | 'CLOSED' | 'CHANNEL_ERROR' | 'disconnected' | 'error' | 'polling';

// Processing status types
export type ProcessingStatus =
  | 'pending_transcripcion'
  | 'pending_asignacion_speakers'
  | 'procesando_ia'
  | 'completado'
  | 'error_transcripcion'
  | 'error_ia'
  | 'error';

// Step types
export type MeetingStep = 1 | 1.5 | 2 | 2.5 | 3;

// Preguntas pendientes types
export interface PreguntaPendiente {
  id_persona?: string | null;
  id_lead_contacto?: string | null;
  pregunta_texto: string;
  info_adicional?: string | null;
}