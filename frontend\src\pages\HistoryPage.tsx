import React, { useState, useEffect } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { useAuth } from '../hooks/useAuth'; // Corrected import path
import { useChat } from '../hooks/useChat'; // Corrected import path
import TitleEditor from '../components/Chat/TitleEditor';
import { API_BASE_URL } from '../config/api';

// Define the structure for the thread summary expected from the backend
// Based on backend/app/models/chat.py::ThreadSummary
interface ThreadSummary {
  thread_id: number;
  last_updated: string; // ISO string date
  preview?: string | null;
  titulo?: string | null; // Thread title from metadata
}

const HistoryPage: React.FC = () => {
  const { getToken } = useAuth();
  const { setCurrentThreadId, clearMessages } = useChat(); // Get context functions
  const navigate = useNavigate();
  const [threads, setThreads] = useState<ThreadSummary[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchThreads = async () => {
      const token = await getToken();
      if (!token) {
        setError("Not authenticated.");
        return;
      }
      setIsLoading(true);
      setError(null);
      try {
        // Construct full URL using environment variable
        const threadsUrl = `${API_BASE_URL}/threads/`;
        console.log("Fetching history from:", threadsUrl);
        const response = await fetch(threadsUrl, { // Fetch thread list
          headers: {
            'Authorization': `Bearer ${token}`,
          },
        });

        if (!response.ok) {
          const errorData = await response.json().catch(() => ({ detail: `HTTP error ${response.status}` }));
          throw new Error(errorData.detail || 'Failed to fetch history');
        }

        const data: ThreadSummary[] = await response.json();
        // Sort by last_updated descending (API might already do this, but good practice)
        data.sort((a, b) => new Date(b.last_updated).getTime() - new Date(a.last_updated).getTime());
        setThreads(data);

      } catch (err) {
        console.error("Error fetching history:", err);
        setError(err instanceof Error ? err.message : 'An unknown error occurred');
      } finally {
        setIsLoading(false);
      }
    };

    fetchThreads();
  }, [getToken]);

  // Function to handle loading a selected thread (Task 13.5)
  const handleLoadThread = (threadId: number) => {
    console.log(`Loading thread: ${threadId}`);
    clearMessages(); // Clear existing messages from context
    setCurrentThreadId(threadId); // Set the selected thread ID in context
    navigate('/chat'); // Navigate back to the chat page
  };

  // Function to update thread title from history
  const handleUpdateTitle = async (threadId: number, newTitle: string) => {
    const token = await getToken();
    if (!token) return;

    try {
      const response = await fetch(`${API_BASE_URL}/threads/${threadId}/metadata`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({ titulo: newTitle })
      });

      if (response.ok) {
        // Update the local threads state
        setThreads(prevThreads =>
          prevThreads.map(thread =>
            thread.thread_id === threadId
              ? { ...thread, titulo: newTitle }
              : thread
          )
        );
      } else {
        console.error('Failed to update title:', response.statusText);
        throw new Error(`Failed to update title: ${response.statusText}`);
      }
    } catch (error) {
      console.error('Error updating thread title:', error);
      throw error;
    }
  };

  // Function to delete a conversation thread
  const handleDeleteThread = async (threadId: number) => {
    const token = await getToken();
    if (!token) return;

    // Show confirmation dialog
    const confirmDelete = window.confirm(
      '¿Estás seguro de que quieres eliminar esta conversación? Esta acción no se puede deshacer.'
    );

    if (!confirmDelete) return;

    try {
      const response = await fetch(`${API_BASE_URL}/threads/${threadId}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (response.ok) {
        // Remove the thread from local state
        setThreads(prevThreads =>
          prevThreads.filter(thread => thread.thread_id !== threadId)
        );

        const result = await response.json();
        console.log(`Successfully deleted thread ${threadId} with ${result.deleted_messages} messages`);
      } else {
        const errorData = await response.json().catch(() => ({ detail: `HTTP error ${response.status}` }));
        throw new Error(errorData.detail || 'Failed to delete conversation');
      }
    } catch (error) {
      console.error('Error deleting thread:', error);
      alert(`Error al eliminar la conversación: ${error instanceof Error ? error.message : 'Error desconocido'}`);
    }
  };

  // Helper to format date
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleString(undefined, { dateStyle: 'medium', timeStyle: 'short' });
  };

  return (
    <div className="h-full overflow-y-auto p-4 md:p-6">
      <div className="max-w-4xl mx-auto">
        <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center mb-6 space-y-4 sm:space-y-0">
          <h1 className="text-2xl md:text-3xl font-bold text-gray-800">Conversation History</h1>
          <Link
            to="/chat"
            className="px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700 text-sm transition-colors self-start sm:self-auto"
          >
            Back to Chat
          </Link>
        </div>

      {isLoading && <p className="text-center text-gray-500">Loading history...</p>}
      {error && <p className="text-center text-red-600">Error: {error}</p>}

      {!isLoading && !error && threads.length === 0 && (
        <p className="text-center text-gray-500">No conversation history found.</p>
      )}

      {!isLoading && !error && threads.length > 0 && (
        <div className="space-y-4">
          {threads.map((thread) => (
            <div
              key={thread.thread_id}
              className="bg-white p-4 rounded-lg shadow-sm border border-gray-200 hover:shadow-md transition-shadow"
            >
              <div className="flex justify-between items-start mb-2">
                <div className="flex-1 min-w-0 mr-4">
                  <TitleEditor
                    title={thread.titulo || `Chat ${thread.thread_id}`}
                    onSave={(newTitle) => handleUpdateTitle(thread.thread_id, newTitle)}
                    className="mb-1"
                    placeholder={`Chat ${thread.thread_id}`}
                  />
                </div>
                <p className="text-xs text-gray-500 flex-shrink-0">{formatDate(thread.last_updated)}</p>
              </div>
              <div className="flex justify-between items-center">
                <p className="text-sm text-gray-600 truncate italic flex-1">
                  {thread.preview || 'No preview available'}
                </p>
                <div className="flex space-x-2 ml-4 flex-shrink-0">
                  <button
                    onClick={() => handleLoadThread(thread.thread_id)}
                    className="px-3 py-1 text-sm bg-indigo-600 text-white rounded hover:bg-indigo-700 transition-colors"
                  >
                    Abrir
                  </button>
                  <button
                    onClick={() => handleDeleteThread(thread.thread_id)}
                    className="px-3 py-1 text-sm bg-red-600 text-white rounded hover:bg-red-700 transition-colors"
                    title="Eliminar conversación"
                  >
                    🗑️
                  </button>
                </div>
              </div>
            </div>
          ))}
        </div>
      )}
      </div>
    </div>
  );
};

export default HistoryPage;