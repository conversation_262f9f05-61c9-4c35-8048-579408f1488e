"""
Estimador de tokens "sin librerías"
Basado en:
 - División por palabras Y signos de puntuación
 - Ajuste empírico a 0,75 (palabras medianas) + 0,25 (puntuación)
"""
import re
from typing import Optional


def estimate_tokens(text: str = '') -> int:
    """
    Estimador de tokens basado en regex
    
    Args:
        text: Texto a analizar
        
    Returns:
        Número estimado de tokens
    """
    if not text or not isinstance(text, str):
        return 0
    
    # 1) Trocea por grupos de letras/números vs. resto
    # Usando regex compatible con Python (sin \p{L} que requiere regex module)
    rough_split = re.findall(r'[a-zA-ZÀ-ÿ0-9]+|[^\s\w]', text)
    
    # 2) Este split suele dar un 10-15 % menos de la cifra real.
    #    Ajustamos con factor 1,15 (validado en español/inglés).
    return round(len(rough_split) * 1.15)


def estimate_tokens_from_messages(messages: list, system_prompt: Optional[str] = None, tools_description: Optional[str] = None) -> int:
    """
    Calcula tokens totales de una conversación
    
    Args:
        messages: Lista de mensajes con campo 'content'
        system_prompt: Prompt del sistema (opcional)
        tools_description: Descripción de herramientas (opcional)
        
    Returns:
        Número total estimado de tokens
    """
    total_text = []
    
    # Agregar system prompt si existe
    if system_prompt:
        total_text.append(system_prompt)
    
    # Agregar descripción de herramientas si existe
    if tools_description:
        total_text.append(tools_description)
    
    # Agregar contenido de todos los mensajes
    for message in messages:
        if hasattr(message, 'content') and message.content:
            total_text.append(str(message.content))
        elif isinstance(message, dict) and message.get('content'):
            total_text.append(str(message['content']))
    
    # Combinar todo el texto
    combined_text = '\n\n'.join(total_text)
    
    return estimate_tokens(combined_text)
