// Chat-related TypeScript types for the frontend

export interface ThreadMetadata {
  thread_id: number;
  titulo: string | null;
  created_at: string; // ISO string date
  updated_at: string; // ISO string date
}

export interface ThreadMetadataUpdate {
  titulo: string;
}

export interface ThreadMetadataCreate {
  thread_id: number;
  titulo?: string; // Optional, defaults to "Chat sin título"
}

// Re-export ChatMessage from context for convenience
export type { ChatMessage } from '../contexts/ChatContext';
