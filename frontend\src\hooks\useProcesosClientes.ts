import { useState, useEffect, useCallback, useMemo } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { supabase } from '../services/supabaseClient';
import apiClient from '../lib/api';
import type {
  ProcesoClienteListResponse,
  ProcesoClienteDetalle,
  ProcesoClienteFilters,
  ProcesoClienteUpdate,
  ProcesoClienteGroupBy,
  ProcesoClienteContadores,
  TareaClienteListResponse,
  TareaClienteFilters,
  TareaClienteUpdate,
  PersonaResponsable,
  DepartamentoInfo,
} from '../types/proceso_cliente';

interface UseProcesosClientesOptions {
  empresaId: string;
  initialFilters?: ProcesoClienteFilters;
  initialGroupBy?: ProcesoClienteGroupBy;
}

interface UseProcesosClientesReturn {
  // Data
  procesos: ProcesoClienteListResponse | undefined;
  contadores: ProcesoClienteContadores | undefined;
  procesoSeleccionado: ProcesoClienteDetalle | undefined;
  tareas: TareaClienteListResponse | undefined;
  personasDisponibles: PersonaResponsable[];
  departamentosDisponibles: DepartamentoInfo[];
  
  // Loading states
  isLoadingProcesos: boolean;
  isLoadingContadores: boolean;
  isLoadingProcesoDetalle: boolean;
  isLoadingTareas: boolean;
  isLoadingPersonas: boolean;
  isLoadingDepartamentos: boolean;
  
  // Error states
  errorProcesos: Error | null;
  errorContadores: Error | null;
  errorProcesoDetalle: Error | null;
  errorTareas: Error | null;
  
  // Filters and grouping
  filters: ProcesoClienteFilters;
  groupBy: ProcesoClienteGroupBy | undefined;
  tareaFilters: TareaClienteFilters;
  
  // Actions
  setFilters: (filters: ProcesoClienteFilters) => void;
  setGroupBy: (groupBy: ProcesoClienteGroupBy | undefined) => void;
  setTareaFilters: (filters: TareaClienteFilters) => void;
  clearFilters: () => void;
  
  // Process actions
  seleccionarProceso: (procesoId: string | null) => void;
  updateProceso: (procesoId: string, data: ProcesoClienteUpdate) => Promise<void>;
  updateTarea: (tareaId: string, data: TareaClienteUpdate) => Promise<void>;
  
  // Panel state
  isPanelVisible: boolean;
  togglePanel: () => void;
  closePanel: () => void;
  
  // Refresh
  refetchProcesos: () => void;
  refetchContadores: () => void;
  refetchProcesoDetalle: () => void;
}

export const useProcesosClientes = ({
  empresaId,
  initialFilters = {},
  initialGroupBy,
}: UseProcesosClientesOptions): UseProcesosClientesReturn => {
  const queryClient = useQueryClient();
  
  // State
  const [filters, setFilters] = useState<ProcesoClienteFilters>(initialFilters);
  const [groupBy, setGroupBy] = useState<ProcesoClienteGroupBy | undefined>(initialGroupBy);
  const [tareaFilters, setTareaFilters] = useState<TareaClienteFilters>({});
  const [procesoSeleccionadoId, setProcesoSeleccionadoId] = useState<string | null>(null);
  const [isPanelVisible, setIsPanelVisible] = useState(false);
  
  // Query keys - memoized to prevent unnecessary re-renders
  const procesosQueryKey = useMemo(() => ['procesos-clientes', empresaId, filters, groupBy], [empresaId, filters, groupBy]);
  const contadoresQueryKey = useMemo(() => ['procesos-clientes-contadores', empresaId], [empresaId]);
  const procesoDetalleQueryKey = useMemo(() => ['proceso-cliente-detalle', procesoSeleccionadoId], [procesoSeleccionadoId]);
  const tareasQueryKey = useMemo(() => ['tareas-cliente', procesoSeleccionadoId, tareaFilters], [procesoSeleccionadoId, tareaFilters]);
  const personasQueryKey = useMemo(() => ['personas-disponibles', empresaId], [empresaId]);
  const departamentosQueryKey = useMemo(() => ['departamentos-disponibles', empresaId], [empresaId]);
  
  // Queries
  const {
    data: procesos,
    isLoading: isLoadingProcesos,
    error: errorProcesos,
    refetch: refetchProcesos,
  } = useQuery({
    queryKey: procesosQueryKey,
    queryFn: () => {
      const params: Record<string, string | boolean> = {};
      
      if (filters.responsable_ids?.length) {
        params.responsable_ids = filters.responsable_ids.join(',');
      }
      if (filters.es_cuello_botella !== undefined) {
        params.es_cuello_botella = filters.es_cuello_botella;
      }
      if (filters.valor_negocio_cliente) {
        params.valor_negocio_cliente = filters.valor_negocio_cliente;
      }
      if (filters.es_manual !== undefined) {
        params.es_manual = filters.es_manual;
      }
      if (filters.es_repetitivo !== undefined) {
        params.es_repetitivo = filters.es_repetitivo;
      }
      if (filters.complejidad_automatizacion_aceleralia) {
        params.complejidad_automatizacion_aceleralia = filters.complejidad_automatizacion_aceleralia;
      }
      if (filters.prioridad_automatizacion_aceleralia) {
        params.prioridad_automatizacion_aceleralia = filters.prioridad_automatizacion_aceleralia;
      }
      if (filters.search) {
        params.search = filters.search;
      }
      if (groupBy) {
        params.group_by = groupBy;
      }
      
      return apiClient.procesosClientes.getByEmpresa(empresaId, params);
    },
    enabled: !!empresaId,
  });
  
  const {
    data: contadores,
    isLoading: isLoadingContadores,
    error: errorContadores,
    refetch: refetchContadores,
  } = useQuery({
    queryKey: contadoresQueryKey,
    queryFn: () => apiClient.procesosClientes.getContadores(empresaId),
    enabled: !!empresaId,
  });
  
  const {
    data: procesoSeleccionado,
    isLoading: isLoadingProcesoDetalle,
    error: errorProcesoDetalle,
    refetch: refetchProcesoDetalle,
  } = useQuery({
    queryKey: procesoDetalleQueryKey,
    queryFn: () => apiClient.procesosClientes.getDetalle(procesoSeleccionadoId!),
    enabled: !!procesoSeleccionadoId,
  });
  
  const {
    data: tareas,
    isLoading: isLoadingTareas,
    error: errorTareas,
  } = useQuery({
    queryKey: tareasQueryKey,
    queryFn: () => {
      const params: Record<string, string | boolean> = {};
      
      if (tareaFilters.responsable_ids?.length) {
        params.responsable_ids = tareaFilters.responsable_ids.join(',');
      }
      if (tareaFilters.es_manual_cliente !== undefined) {
        params.es_manual_cliente = tareaFilters.es_manual_cliente;
      }
      if (tareaFilters.search) {
        params.search = tareaFilters.search;
      }
      
      return apiClient.procesosClientes.getTareas(procesoSeleccionadoId!, params);
    },
    enabled: !!procesoSeleccionadoId,
  });
  
  const {
    data: personasData,
    isLoading: isLoadingPersonas,
  } = useQuery({
    queryKey: personasQueryKey,
    queryFn: () => apiClient.procesosClientes.getPersonasDisponibles(empresaId),
    enabled: !!empresaId,
  });
  
  const {
    data: departamentosData,
    isLoading: isLoadingDepartamentos,
  } = useQuery({
    queryKey: departamentosQueryKey,
    queryFn: () => apiClient.procesosClientes.getDepartamentosDisponibles(empresaId),
    enabled: !!empresaId,
  });
  
  // Mutations
  const updateProcesoMutation = useMutation({
    mutationFn: ({ procesoId, data }: { procesoId: string; data: ProcesoClienteUpdate }) =>
      apiClient.procesosClientes.update(procesoId, data),
    onSuccess: () => {
      // Invalidate related queries
      queryClient.invalidateQueries({ queryKey: procesosQueryKey });
      queryClient.invalidateQueries({ queryKey: contadoresQueryKey });
      queryClient.invalidateQueries({ queryKey: procesoDetalleQueryKey });
    },
  });
  
  const updateTareaMutation = useMutation({
    mutationFn: ({ tareaId, data }: { tareaId: string; data: TareaClienteUpdate }) =>
      apiClient.tareasClientes.update(tareaId, data),
    onSuccess: () => {
      // Invalidate related queries
      queryClient.invalidateQueries({ queryKey: tareasQueryKey });
      queryClient.invalidateQueries({ queryKey: procesoDetalleQueryKey });
      queryClient.invalidateQueries({ queryKey: procesosQueryKey });
      queryClient.invalidateQueries({ queryKey: contadoresQueryKey });
    },
  });
  
  // Actions
  const seleccionarProceso = useCallback((procesoId: string | null) => {
    setProcesoSeleccionadoId(procesoId);
    setIsPanelVisible(!!procesoId);
    if (procesoId) {
      // Reset task filters when selecting a new process
      setTareaFilters({});
    }
  }, []);
  
  const updateProceso = useCallback(async (procesoId: string, data: ProcesoClienteUpdate) => {
    await updateProcesoMutation.mutateAsync({ procesoId, data });
  }, [updateProcesoMutation]);
  
  const updateTarea = useCallback(async (tareaId: string, data: TareaClienteUpdate) => {
    await updateTareaMutation.mutateAsync({ tareaId, data });
  }, [updateTareaMutation]);
  
  const clearFilters = useCallback(() => {
    setFilters({});
    setGroupBy(undefined);
  }, []);
  
  const togglePanel = useCallback(() => {
    setIsPanelVisible(prev => !prev);
  }, []);
  
  const closePanel = useCallback(() => {
    setIsPanelVisible(false);
    setProcesoSeleccionadoId(null);
  }, []);

  // Real-time subscriptions
  useEffect(() => {
    if (!empresaId) return;

    console.log('Setting up Procesos Clientes realtime subscription for empresa:', empresaId);

    let procesosSubscription: ReturnType<typeof supabase.channel> | null = null;
    let tareasSubscription: ReturnType<typeof supabase.channel> | null = null;

    const setupRealtimeSubscriptions = () => {
      // Subscribe to procesos_clientes table changes
      procesosSubscription = supabase
        .channel(`procesos_clientes_empresa_${empresaId}`)
        .on(
          'postgres_changes',
          {
            event: '*',
            schema: 'public',
            table: 'procesos_clientes',
            filter: `empresa_cliente_id=eq.${empresaId}`,
          },
          (payload) => {
            console.log('Procesos Clientes real-time update:', payload);
            // Invalidate and refetch queries
            queryClient.invalidateQueries({ queryKey: procesosQueryKey });
            queryClient.invalidateQueries({ queryKey: contadoresQueryKey });
            if (payload.eventType === 'UPDATE' && payload.new?.id === procesoSeleccionadoId) {
              queryClient.invalidateQueries({ queryKey: procesoDetalleQueryKey });
            }
          }
        )
        .subscribe((status) => {
          console.log('Procesos Clientes subscription status:', status);
        });

      // Subscribe to tareas_clientes table changes
      tareasSubscription = supabase
        .channel(`tareas_clientes_empresa_${empresaId}`)
        .on(
          'postgres_changes',
          {
            event: '*',
            schema: 'public',
            table: 'tareas_clientes',
          },
          (payload) => {
            console.log('Tareas Clientes real-time update:', payload);
            // Invalidate and refetch queries
            queryClient.invalidateQueries({ queryKey: tareasQueryKey });
            queryClient.invalidateQueries({ queryKey: procesoDetalleQueryKey });
            queryClient.invalidateQueries({ queryKey: procesosQueryKey });
            queryClient.invalidateQueries({ queryKey: contadoresQueryKey });
          }
        )
        .subscribe((status) => {
          console.log('Tareas Clientes subscription status:', status);
        });
    };

    setupRealtimeSubscriptions();

    return () => {
      if (procesosSubscription) {
        console.log('Cleaning up Procesos Clientes realtime subscription');
        supabase.removeChannel(procesosSubscription);
      }
      if (tareasSubscription) {
        console.log('Cleaning up Tareas Clientes realtime subscription');
        supabase.removeChannel(tareasSubscription);
      }
    };
  }, [empresaId, queryClient, procesosQueryKey, contadoresQueryKey, procesoDetalleQueryKey, tareasQueryKey, procesoSeleccionadoId]);

  return {
    // Data
    procesos,
    contadores,
    procesoSeleccionado,
    tareas,
    personasDisponibles: personasData?.personas || [],
    departamentosDisponibles: departamentosData?.departamentos || [],
    
    // Loading states
    isLoadingProcesos,
    isLoadingContadores,
    isLoadingProcesoDetalle,
    isLoadingTareas,
    isLoadingPersonas,
    isLoadingDepartamentos,
    
    // Error states
    errorProcesos,
    errorContadores,
    errorProcesoDetalle,
    errorTareas,
    
    // Filters and grouping
    filters,
    groupBy,
    tareaFilters,
    
    // Actions
    setFilters,
    setGroupBy,
    setTareaFilters,
    clearFilters,
    
    // Process actions
    seleccionarProceso,
    updateProceso,
    updateTarea,
    
    // Panel state
    isPanelVisible,
    togglePanel,
    closePanel,
    
    // Refresh
    refetchProcesos,
    refetchContadores,
    refetchProcesoDetalle,
  };
};
