import React, { useState, useEffect } from 'react';
import { useChat } from '../../hooks/useChat';
import { useAuth } from '../../hooks/useAuth';
import { API_BASE_URL } from '../../config/api';

interface LLMModel {
  id: string;
  model_name: string;
}

const LLMModelSelector: React.FC = () => {
  const { selectedModelName, setSelectedModelName } = useChat();
  const { session } = useAuth();
  const [models, setModels] = useState<LLMModel[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchModels = async () => {
      setIsLoading(true);
      setError(null);
      if (!session?.access_token) {
        setError("Not authenticated.");
        setIsLoading(false);
        return;
      }
      try {
        const apiUrl = `${API_BASE_URL}/llm-models`;
        const response = await fetch(apiUrl, {
          headers: { 'Authorization': `Bearer ${session.access_token}` },
        });
        if (!response.ok) {
          const errorData = await response.json().catch(() => ({ detail: `HTTP error ${response.status}` }));
          throw new Error(errorData.detail || 'Failed to fetch LLM models');
        }
        const data: LLMModel[] = await response.json();
        setModels(data);
      } catch (err) {
        console.error("Error fetching LLM models:", err);
        setError(err instanceof Error ? err.message : 'An unknown error occurred');
      } finally {
        setIsLoading(false);
      }
    };
    fetchModels();
  }, [session]);

  const handleSelectChange = (event: React.ChangeEvent<HTMLSelectElement>) => {
    const newModelName = event.target.value || null;
    setSelectedModelName(newModelName);
  };

  return (
    <div className="space-y-4">
      <div>
        <label htmlFor="llm-model-select" className="block text-sm font-medium text-gray-700 mb-2">
          Seleccionar Modelo LLM
        </label>
        {isLoading && (
          <div className="flex items-center space-x-2">
            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-indigo-600"></div>
            <p className="text-sm text-gray-500">Cargando modelos...</p>
          </div>
        )}
        {error && (
          <div className="bg-red-50 border border-red-200 rounded-md p-3">
            <p className="text-sm text-red-600">Error: {error}</p>
          </div>
        )}
        {!isLoading && !error && (
          <select
            id="llm-model-select"
            value={selectedModelName || ''}
            onChange={handleSelectChange}
            disabled={isLoading}
            className="block w-full pl-3 pr-10 py-4 text-base border-gray-300 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 rounded-lg shadow-sm disabled:bg-gray-100 transition-colors min-h-[44px]"
          >
            <option value="" disabled={models.length > 0}>
              {models.length === 0 ? 'No hay modelos disponibles' : '-- Selecciona un Modelo --'}
            </option>
            {models.map((model) => (
              <option key={model.id} value={model.model_name}>
                {model.model_name}
              </option>
            ))}
          </select>
        )}
      </div>
    </div>
  );
};

export default LLMModelSelector;