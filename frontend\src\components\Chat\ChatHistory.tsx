import React, { useEffect, useRef, useMemo } from 'react';
import MessageBubble from './MessageBubble';
import { useChat } from '../../hooks/useChat';

const ChatHistory: React.FC = () => {
  const { messages, isProcessingMessage, isLoadingHistory } = useChat();
  const messagesEndRef = useRef<HTMLDivElement>(null);

  // Function to scroll to the bottom
  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  };

  // Scroll to bottom whenever messages change
  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  // Optimización: solo renderizar los últimos N mensajes si hay muchos
  const visibleMessages = useMemo(() => {
    if (messages.length <= 50) {
      return messages;
    }
    // Para chats muy largos, mostrar solo los últimos 50 mensajes
    return messages.slice(-50);
  }, [messages]);

  return (
    <div className="h-full overflow-y-auto p-6 sm:p-8 space-y-8 bg-gradient-to-b from-gray-50 to-white">
      {/* Show loading indicator when fetching history */}
      {isLoadingHistory && (
        <div className="flex justify-center items-center min-h-[200px]">
          <div className="flex items-center space-x-3">
            <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-indigo-600"></div>
            <p className="text-gray-500 italic">Cargando historial...</p>
          </div>
        </div>
      )}

      {/* Show messages only when not loading history */}
      {!isLoadingHistory && messages.length === 0 && (
        <div className="flex flex-col justify-center items-center min-h-[400px] text-center">
          <div className="bg-white rounded-2xl p-8 shadow-sm border border-gray-100 max-w-md">
            <div className="text-6xl mb-4">💬</div>
            <h3 className="text-lg font-medium text-gray-800 mb-2">¡Comienza una conversación!</h3>
            <p className="text-gray-500 text-sm">Selecciona un agente y envía tu primer mensaje para comenzar.</p>
          </div>
        </div>
      )}

      {/* Mostrar indicador si hay mensajes ocultos */}
      {!isLoadingHistory && messages.length > 50 && (
        <div className="flex justify-center mb-4">
          <div className="bg-blue-50 border border-blue-200 rounded-lg px-4 py-2 text-sm text-blue-700">
            Mostrando los últimos 50 mensajes de {messages.length} total
          </div>
        </div>
      )}

      {!isLoadingHistory && visibleMessages.map((msg, index) => (
        <MessageBubble
          key={`${msg.thread_id}-${msg.message_id}-${index}-${msg.created_at}`}
          message={msg}
        />
      ))}

      {/* Placeholder for "Thinking..." indicator */}
      {isProcessingMessage && !isLoadingHistory && (
        <div className="flex justify-start mb-4">
          <div className="w-full max-w-4xl px-4 py-4 rounded-2xl bg-gradient-to-r from-indigo-50 to-purple-50 text-gray-700 border border-indigo-100 shadow-lg">
            <div className="flex items-center space-x-3">
              <div className="flex space-x-1">
                <div className="w-3 h-3 bg-indigo-400 rounded-full animate-bounce"></div>
                <div className="w-3 h-3 bg-indigo-400 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
                <div className="w-3 h-3 bg-indigo-400 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
              </div>
              <span className="text-sm font-medium">El agente está pensando...</span>
              <div className="ml-auto">
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-indigo-600"></div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Empty div at the end to scroll to */}
      <div ref={messagesEndRef} />
    </div>
  );
};

export default ChatHistory;