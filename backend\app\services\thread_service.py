import logging
import json
from uuid import UUID
from typing import List, Dict, Set
from datetime import datetime

from fastapi import H<PERSON><PERSON><PERSON><PERSON><PERSON>, status
from supabase import AsyncClient

from app.core.database import get_supabase_client
from app.models.chat import ThreadSummary, ChatMessage

logger = logging.getLogger("app.services.thread_service")

async def get_threads_for_user(user_id: UUID) -> List[ThreadSummary]:
    try:
        supabase: AsyncClient = await get_supabase_client()
        response = supabase.table("threads").select("thread_id, created_at, content").eq("user_id", user_id).order("created_at", desc=True).execute()
        if not response.data:
            return []
        
        threads_summary_map: Dict[int, ThreadSummary] = {}
        for message_data in response.data:
            thread_id = message_data.get("thread_id")
            created_at = message_data.get("created_at")
            content = message_data.get("content")
            if thread_id is not None and created_at is not None and thread_id not in threads_summary_map:
                last_updated_dt = datetime.fromisoformat(created_at.replace("Z", "+00:00"))
                threads_summary_map[thread_id] = ThreadSummary(
                    thread_id=thread_id,
                    last_updated=last_updated_dt,
                    preview=str(content)[:100] + "..." if content and len(str(content)) > 100 else str(content) if content else "",
                    titulo=None
                )
        
        if threads_summary_map:
            thread_ids = list(threads_summary_map.keys())
            metadata_response = supabase.table("threads_metadata").select("thread_id, titulo").in_("thread_id", thread_ids).execute()
            if metadata_response.data:
                for metadata in metadata_response.data:
                    thread_id = metadata.get("thread_id")
                    if thread_id in threads_summary_map:
                        threads_summary_map[thread_id].titulo = metadata.get("titulo")
        
        return list(threads_summary_map.values())
    except Exception as e:
        logger.exception(f"Database error while fetching threads for user {user_id}: {e}")
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=f"An error occurred while fetching chat threads: {e}")

async def check_thread_authorization(thread_id: int, user_id: UUID) -> bool:
    try:
        supabase: AsyncClient = await get_supabase_client()
        response = supabase.table("threads").select("user_id", count="exact").eq("thread_id", thread_id).eq("user_id", user_id).limit(1).execute()
        return response.count > 0
    except Exception as e:
        logger.exception(f"Database error checking thread authorization for thread {thread_id}, user {user_id}: {e}")
        return False

async def get_messages_for_thread(thread_id: int, limit: int = 50, offset: int = 0, load_recent: bool = True) -> List[ChatMessage]:
    try:
        supabase: AsyncClient = await get_supabase_client()
        query = supabase.table("threads").select("*").eq("thread_id", thread_id)
        if load_recent:
            query = query.order("created_at", desc=True)
        else:
            query = query.order("created_at", desc=False)
        
        query = query.range(offset, offset + limit - 1)
        response = query.execute()

        if not response.data:
            return []

        messages_data = response.data
        tool_ids_to_fetch: Set[UUID] = set()
        for msg_data in messages_data:
            if msg_data.get("type") == "tool_use" and msg_data.get("content"):
                try:
                    content_json = json.loads(msg_data["content"])
                    tool_input = content_json.get("toolInput", {})
                    tool_id_str = tool_input.get("tool_id")
                    if not tool_id_str:
                        tool_params_str = tool_input.get("tool_params")
                        if isinstance(tool_params_str, str):
                            tool_params_json = json.loads(tool_params_str)
                            tool_id_str = tool_params_json.get("tool_id")
                    if tool_id_str:
                        tool_ids_to_fetch.add(UUID(tool_id_str))
                except (json.JSONDecodeError, ValueError):
                    continue

        tool_name_map: Dict[str, str] = {}
        if tool_ids_to_fetch:
            tool_ids_str_list = [str(uid) for uid in tool_ids_to_fetch]
            tools_response = supabase.table("tools").select("id, tool_name").in_("id", tool_ids_str_list).execute()
            if tools_response.data:
                for tool_data in tools_response.data:
                    tool_name_map[str(tool_data['id'])] = tool_data['tool_name']

        validated_messages: List[ChatMessage] = []
        for msg_data in messages_data:
            current_msg_data = msg_data.copy()
            if current_msg_data.get("type") == "tool_use" and current_msg_data.get("content"):
                try:
                    content_json = json.loads(current_msg_data["content"])
                    tool_input = content_json.get("toolInput", {})
                    tool_id_str = tool_input.get("tool_id")
                    if not tool_id_str:
                        tool_params_str = tool_input.get("tool_params")
                        if isinstance(tool_params_str, str):
                            tool_params_json = json.loads(tool_params_str)
                            tool_id_str = tool_params_json.get("tool_id")
                    if tool_id_str:
                        tool_uuid_str_key = str(UUID(tool_id_str))
                        if tool_uuid_str_key in tool_name_map:
                            current_msg_data['tool_name'] = tool_name_map[tool_uuid_str_key]
                except (json.JSONDecodeError, ValueError, TypeError):
                    pass
            validated_messages.append(ChatMessage.model_validate(current_msg_data))
        
        if load_recent and validated_messages:
            validated_messages.reverse()
        
        return validated_messages
    except Exception as e:
        logger.exception(f"Database error fetching messages for thread {thread_id}: {e}")
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=f"An error occurred while fetching messages for thread {thread_id}: {e}")

async def get_thread_message_count(thread_id: int) -> int:
    try:
        supabase: AsyncClient = await get_supabase_client()
        response = supabase.table("threads").select("*", count="exact").eq("thread_id", thread_id).execute()
        return response.count if response.count is not None else 0
    except Exception as e:
        logger.exception(f"Database error getting message count for thread {thread_id}: {e}")
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=f"An error occurred while getting message count for thread {thread_id}: {e}")

async def delete_entire_thread(thread_id: int, user_id: UUID) -> int:
    try:
        supabase: AsyncClient = await get_supabase_client()
        is_authorized = await check_thread_authorization(thread_id=thread_id, user_id=user_id)
        if not is_authorized:
            raise HTTPException(status_code=status.HTTP_403_FORBIDDEN, detail="Not authorized to delete this thread")
        
        try:
            delete_response = supabase.rpc('delete_thread_atomic', {'p_thread_id': thread_id, 'p_user_id': str(user_id)}).execute()
            if not delete_response.data:
                raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="Failed to delete thread")
            result = delete_response.data
            return result.get('message_count', 0) if isinstance(result, dict) else 0
        except Exception:
            count_response = supabase.table("threads").select("*", count="exact").eq("thread_id", thread_id).execute()
            message_count = count_response.count if count_response.count is not None else 0
            supabase.table("threads_metadata").delete().eq("thread_id", thread_id).execute()
            supabase.table("threads").delete().eq("thread_id", thread_id).execute()
            return message_count
    except HTTPException:
        raise
    except Exception as e:
        logger.exception(f"Database error deleting thread {thread_id} for user {user_id}: {e}")
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=f"An error occurred while deleting thread {thread_id}: {e}")