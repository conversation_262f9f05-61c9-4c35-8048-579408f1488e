import React, { useEffect } from 'react';
import { Link } from 'react-router-dom';
import { IoMenu, IoTimeOutline, IoAddCircleOutline } from 'react-icons/io5';
import ChatInput from '../components/Chat/ChatInput';
import VirtualChatHistory from '../components/Chat/VirtualChatHistory';
import ContextPanel from '../components/Chat/ContextPanel';
import TitleEditor from '../components/Chat/TitleEditor';
import { useChat } from '../hooks/useChat';
import { useMobileDetection } from '../hooks/useMobileDetection';

const ChatPage: React.FC = () => {
  const {
    isContextPanelOpen,
    toggleContextPanel,
    currentThreadId,
    startNewConversation,
    isAgentSelectionRequired,
    totalTokens,
    totalCost,
    currentThreadTitle,
    updateThreadTitle,
  } = useChat();

  const { isIOS } = useMobileDetection();

  useEffect(() => {
    if (isIOS) {
      const setVH = () => {
        const vh = window.innerHeight * 0.01;
        document.documentElement.style.setProperty('--vh', `${vh}px`);
      };
      setVH();
      window.addEventListener('resize', setVH);
      window.addEventListener('orientationchange', () => setTimeout(setVH, 100));
      return () => {
        window.removeEventListener('resize', setVH);
        window.removeEventListener('orientationchange', setVH);
      };
    }
  }, [isIOS]);

  const isPanelEffectivelyOpen = isContextPanelOpen || isAgentSelectionRequired;

  return (
    <div className={`flex w-full overflow-hidden bg-white ${isIOS ? 'ios-vh-fix' : 'h-full'}`} style={isIOS ? { height: 'calc(var(--vh, 1vh) * 100)' } : {}}>
      <ContextPanel
        isOpen={isPanelEffectivelyOpen}
        onClose={toggleContextPanel}
      />
      <header className="md:hidden fixed top-0 left-0 right-0 z-50 bg-white border-b border-gray-200 shadow-sm mobile-chat-header pt-safe">
        <div className="flex items-center justify-between p-3">
          <button onClick={toggleContextPanel} className={`p-2 rounded-full transition-all duration-300 ${isPanelEffectivelyOpen ? 'bg-indigo-600 text-white' : 'bg-gray-100 text-gray-700 hover:bg-gray-200'}`} aria-label={isPanelEffectivelyOpen ? "Cerrar menú" : "Abrir menú"}>
            <IoMenu size={20} />
          </button>
          <div className="flex-1 px-3 min-w-0">
            {currentThreadId ? (
              <TitleEditor title={currentThreadTitle || 'Chat sin título'} onSave={updateThreadTitle} className="text-center" placeholder="Chat sin título" />
            ) : (
              <h1 className="text-base font-semibold text-gray-800 truncate text-center">Nuevo Chat</h1>
            )}
          </div>
          <div className="flex items-center space-x-1">
            <button onClick={startNewConversation} className="p-2 rounded-full text-gray-500 hover:text-gray-700 hover:bg-gray-100 transition-colors" aria-label="Nuevo chat">
              <IoAddCircleOutline size={20} />
            </button>
            <Link to="/history" className="p-2 rounded-full text-gray-500 hover:text-gray-700 hover:bg-gray-100 transition-colors" aria-label="Historial">
              <IoTimeOutline size={20} />
            </Link>
          </div>
        </div>
      </header>
      <div className={`flex flex-col flex-1 min-w-0 transition-all duration-300 ease-in-out ${isPanelEffectivelyOpen ? 'md:ml-1/2' : 'ml-0'} ${isIOS ? 'ios-vh-fix' : 'h-full'}`} style={isIOS ? { height: 'calc(var(--vh, 1vh) * 100)' } : {}}>
        <header className="hidden md:flex items-center justify-between p-4 bg-gray-50 border-b border-gray-200 flex-shrink-0">
          <div className="flex items-center space-x-2 min-w-0">
            <button onClick={toggleContextPanel} className={`p-2 rounded-full flex-shrink-0 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-colors ${isPanelEffectivelyOpen ? 'text-indigo-600 bg-indigo-100 hover:bg-indigo-200' : 'text-gray-500 hover:text-gray-700 hover:bg-gray-200'}`} aria-label={isPanelEffectivelyOpen ? "Cerrar panel de contexto" : "Abrir panel de contexto"}>
              <IoMenu size={24} />
            </button>
            <div className="min-w-0 flex-1">
              {currentThreadId ? (
                <TitleEditor title={currentThreadTitle || 'Chat sin título'} onSave={updateThreadTitle} className="mb-1" placeholder="Chat sin título" />
              ) : (
                <h1 className="text-lg md:text-xl font-semibold text-gray-800 truncate">Nuevo Chat</h1>
              )}
            </div>
          </div>
          {currentThreadId && (
            <div className="hidden sm:flex items-center justify-center flex-1 max-w-md mx-4">
              <div className="flex items-center space-x-4 bg-white border border-gray-200 rounded-full px-4 py-2 shadow-sm">
                <div className="flex items-center">
                  <span className="text-sm font-medium text-gray-500 mr-2">Tokens:</span>
                  <span className="text-sm font-semibold text-indigo-600">{totalTokens.toLocaleString()}</span>
                </div>
                <div className="h-5 w-px bg-gray-300"></div>
                <div className="flex items-center">
                  <span className="text-sm font-medium text-gray-500 mr-2">Coste:</span>
                  <span className="text-sm font-semibold text-green-600">${totalCost.toFixed(5)}</span>
                </div>
              </div>
            </div>
          )}
          <div className="flex items-center space-x-2 flex-shrink-0">
            <button onClick={startNewConversation} className="flex items-center space-x-1 p-2 rounded-full text-gray-500 hover:text-gray-700 hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500" aria-label="Start new conversation">
              <IoAddCircleOutline size={24} />
              <span className="hidden sm:inline text-sm">New</span>
            </button>
            <Link to="/history" className="p-2 rounded-full text-gray-500 hover:text-gray-700 hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500" aria-label="View conversation history">
              <IoTimeOutline size={24} />
            </Link>
          </div>
        </header>
        <div className="flex-1 min-h-0 overflow-y-auto mobile-scroll pt-14 md:pt-0">
          <VirtualChatHistory />
        </div>
        <div className="flex-shrink-0">
          <ChatInput />
        </div>
      </div>
    </div>
  );
};

export default ChatPage;