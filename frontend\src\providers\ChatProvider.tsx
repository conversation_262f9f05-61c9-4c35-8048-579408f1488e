import React, { useState, useEffect, ReactNode, useCallback, useMemo } from 'react';
import { RealtimePostgresChangesPayload } from '@supabase/supabase-js';
import { supabase } from '../services/supabaseClient';
import { ChatContext, ChatContextProps, ChatMessage } from '../contexts/ChatContext';
import { useAuth } from '../hooks/useAuth';
import { useThreadHistory } from '../hooks/useThreadHistory';
import { ThreadMetadata } from '../types/chat';
import { API_BASE_URL } from '../config/api';
import { AgentDetails } from '../components/Chat/AgentSelector';


interface ChatProviderProps {
  children: ReactNode;
}

interface RawChatMessage {
  thread_id: number | string | bigint;
  content: string | null;
  type: string | null;
  from: 'User' | 'Agent' | null;
  message_id: number | null;
  agent_id: string | null;
  user_id: string | null;
  created_at: string;
  id?: string;
  input_token_cost?: number | null;
  output_token_cost?: number | null;
}

export const ChatProvider: React.FC<ChatProviderProps> = ({ children }) => {
  const { getToken } = useAuth();
  const [currentThreadId, setCurrentThreadId] = useState<number | null>(null);
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [selectedAgentId, setSelectedAgentId] = useState<string | null>(null);
  const [isProcessingMessage, setIsProcessingMessage] = useState<boolean>(false);
  const [isContextPanelOpen, setIsContextPanelOpen] = useState<boolean>(false);
  const [isAgentSelectionRequired, setIsAgentSelectionRequired] = useState<boolean>(false);
  const [currentThreadTitle, setCurrentThreadTitle] = useState<string | null>(null);
  const [isLoadingTitle, setIsLoadingTitle] = useState<boolean>(false);
  const [chatInputText, setChatInputText] = useState('');
  const [selectedAgentDetails, setSelectedAgentDetails] = useState<AgentDetails | null>(null);
  const [totalTokens, setTotalTokens] = useState(0);
  const [totalCost, setTotalCost] = useState(0);
  const [selectedModelName, setSelectedModelName] = useState<string | null>(null);
  const { data: historyData, isLoading: isLoadingHistory } = useThreadHistory(currentThreadId);

  const handleSetSelectedAgentId = useCallback((agentId: string | null) => {
    setSelectedAgentId(agentId);
    if (agentId) setIsAgentSelectionRequired(false);
  }, []);

  // Función para refrescar el contador de tokens y costo
  const refreshTokenCount = useCallback(async () => {
    if (!currentThreadId) {
      setTotalTokens(0);
      setTotalCost(0);
      return;
    }

    try {
      const token = await getToken();
      if (!token) return;

      // Obtener el conteo de tokens y costo desde el backend
      const response = await fetch(`${API_BASE_URL}/threads/${currentThreadId}/tokens`, {
        headers: { 'Authorization': `Bearer ${token}` }
      });

      if (!response.ok) {
        console.error('Error fetching token count from backend');
        return;
      }

      const tokenData = await response.json();
      setTotalTokens(tokenData.total_tokens || 0);
      setTotalCost(tokenData.total_cost || 0);
    } catch (error) {
      console.error('Error fetching token count:', error);
    }
  }, [currentThreadId, getToken]);

  const addMessage = useCallback((message: ChatMessage) => {
    setMessages((prev) => [...prev, message]);
    // Actualizar contador de tokens cuando se agrega un mensaje del usuario
    if (message.from_sender === 'User') {
      // Usar setTimeout para permitir que el mensaje se agregue primero
      setTimeout(() => refreshTokenCount(), 100);
    }
  }, [refreshTokenCount]);

  const clearMessages = useCallback(() => setMessages([]), []);
  const startProcessing = useCallback(() => setIsProcessingMessage(true), []);
  const stopProcessing = useCallback(() => setIsProcessingMessage(false), []);
  const toggleContextPanel = useCallback(() => setIsContextPanelOpen((p) => !p), []);

  const startNewConversation = useCallback(() => {
    setCurrentThreadId(null);
    setMessages([]);
    setIsProcessingMessage(false);
    setSelectedAgentId(null);
    setSelectedAgentDetails(null);
    setChatInputText('');
    setSelectedModelName(null);
    setIsAgentSelectionRequired(true);
    setCurrentThreadTitle(null);
  }, []);

  const fetchThreadMetadata = useCallback(async (threadId: number) => {
    const token = await getToken();
    if (!token) return;
    try {
      setIsLoadingTitle(true);
      const response = await fetch(`${API_BASE_URL}/threads/${threadId}/metadata`, { headers: { 'Authorization': `Bearer ${token}` } });
      if (response.ok) {
        const metadata: ThreadMetadata = await response.json();
        setCurrentThreadTitle(metadata.titulo);
      } else {
        setCurrentThreadTitle('Chat sin título');
      }
    } catch {
      setCurrentThreadTitle('Chat sin título');
    } finally {
      setIsLoadingTitle(false);
    }
  }, [getToken]);

  const updateThreadTitle = useCallback(async (newTitle: string) => {
    const token = await getToken();
    if (!currentThreadId || !token) throw new Error('Authentication or thread ID missing');
    setIsLoadingTitle(true);
    try {
      const response = await fetch(`${API_BASE_URL}/threads/${currentThreadId}/metadata`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json', 'Authorization': `Bearer ${token}` },
        body: JSON.stringify({ titulo: newTitle }),
      });
      if (!response.ok) throw new Error('Failed to update title');
      const updatedMetadata: ThreadMetadata = await response.json();
      setCurrentThreadTitle(updatedMetadata.titulo);
    } finally {
      setIsLoadingTitle(false);
    }
  }, [currentThreadId, getToken]);

  useEffect(() => {
    if (currentThreadId) fetchThreadMetadata(currentThreadId);
    else setCurrentThreadTitle(null);
  }, [currentThreadId, fetchThreadMetadata]);

  useEffect(() => {
    const fetchAgentDetails = async () => {
      const token = await getToken();
      if (!selectedAgentId || !token) {
        setSelectedAgentDetails(null);
        return;
      }
      try {
        const response = await fetch(`${API_BASE_URL}/agents/${selectedAgentId}`, { headers: { 'Authorization': `Bearer ${token}` } });
        if (!response.ok) throw new Error('Failed to fetch agent details');
        const data: AgentDetails = await response.json();
        setSelectedAgentDetails(data);
      } catch {
        setSelectedAgentDetails(null);
      }
    };
    fetchAgentDetails();
  }, [selectedAgentId, getToken]);



  // Refrescar tokens cuando cambia el thread
  useEffect(() => {
    refreshTokenCount();
  }, [refreshTokenCount]);

  // El cálculo del costo ahora se hace en el backend a través de refreshTokenCount

  const groupMessages = useCallback((allMessages: ChatMessage[]): ChatMessage[] => {
    const messageMap: { [key: number]: ChatMessage } = {};
    const finalGroupedMessages: ChatMessage[] = [];
    allMessages.forEach(msg => {
      const messageIdNum = typeof msg.message_id === 'string' ? parseInt(msg.message_id, 10) : msg.message_id;
      if (msg.from_sender === 'User' || msg.type === 'answer') {
        const messageToAdd = { ...msg, message_id: messageIdNum, intermediate_steps: msg.type === 'answer' ? [] : undefined };
        if (msg.type === 'answer' && messageIdNum) messageMap[messageIdNum] = messageToAdd;
        finalGroupedMessages.push(messageToAdd);
      } else if (msg.from_sender === 'Agent' && messageIdNum) {
        if (!messageMap[messageIdNum]) messageMap[messageIdNum] = { ...msg, message_id: messageIdNum, intermediate_steps: [] };
        messageMap[messageIdNum].intermediate_steps!.push(msg);
      } else {
        finalGroupedMessages.push({ ...msg, message_id: messageIdNum });
      }
    });
    const updatedFinalMessages = finalGroupedMessages.map(msg => (msg.type === 'answer' && msg.message_id && messageMap[msg.message_id]) ? messageMap[msg.message_id] : msg);
    updatedFinalMessages.sort((a, b) => new Date(a.created_at).getTime() - new Date(b.created_at).getTime());
    return updatedFinalMessages;
  }, []);

  useEffect(() => {
    if (historyData) setMessages(groupMessages(historyData));
    else if (!isLoadingHistory) setMessages([]);
  }, [historyData, isLoadingHistory, groupMessages]);

  useEffect(() => {
    if (messages.length > 0 && !selectedAgentId) {
      const lastAgentMessage = [...messages].reverse().find(msg => msg.from_sender === 'Agent' && msg.agent_id);
      if (lastAgentMessage?.agent_id) setSelectedAgentId(lastAgentMessage.agent_id);
    }
  }, [messages, selectedAgentId]);

  const handleRealtimeMessage = useCallback((payload: RealtimePostgresChangesPayload<RawChatMessage>) => {
    const messageData = payload.new as RawChatMessage;
    if (!messageData || !currentThreadId || messageData.thread_id?.toString() !== currentThreadId.toString()) return;
    
    const mappedMessage: ChatMessage = { ...messageData, id: messageData.id?.toString(), from_sender: messageData.from };
    
    if (mappedMessage.from_sender === 'Agent') {
      const messageIdNum = typeof mappedMessage.message_id === 'string' ? parseInt(mappedMessage.message_id, 10) : mappedMessage.message_id;
      const messageWithNumericId = { ...mappedMessage, message_id: messageIdNum };
      
      if (messageWithNumericId.type === 'answer') {
        setMessages(prev => [...prev, { ...messageWithNumericId, intermediate_steps: [] }]);
        stopProcessing();
        // Actualizar contador de tokens cuando se recibe la respuesta final
        refreshTokenCount();
      } else if (messageWithNumericId.message_id) {
        setMessages(prev => prev.map(msg =>
          (msg.type === 'answer' && msg.message_id === messageWithNumericId.message_id)
            ? { ...msg, intermediate_steps: [...(msg.intermediate_steps || []), messageWithNumericId].sort((a, b) => new Date(a.created_at).getTime() - new Date(b.created_at).getTime()) }
            : msg
        ));
      }
    }
  }, [currentThreadId, stopProcessing, refreshTokenCount]);

  useEffect(() => {
    if (!currentThreadId) return;
    const channel = supabase.channel(`thread_changes:${currentThreadId}`);
    channel.on<RawChatMessage>('postgres_changes', { event: '*', schema: 'public', table: 'threads', filter: `thread_id=eq.${currentThreadId}` }, handleRealtimeMessage).subscribe();
    return () => { supabase.removeChannel(channel); };
  }, [currentThreadId, handleRealtimeMessage]);

  const contextValue: ChatContextProps = useMemo(() => ({
    currentThreadId, setCurrentThreadId, messages, selectedAgentId, setSelectedAgentId: handleSetSelectedAgentId,
    isProcessingMessage, isLoadingHistory, isContextPanelOpen, isAgentSelectionRequired, currentThreadTitle,
    isLoadingTitle, chatInputText, setChatInputText, selectedAgentDetails, setSelectedAgentDetails, totalTokens,
    totalCost, selectedModelName, setSelectedModelName, addMessage, clearMessages, startProcessing,
    stopProcessing, toggleContextPanel, startNewConversation, updateThreadTitle,
  }), [
    currentThreadId, messages, selectedAgentId, handleSetSelectedAgentId, isProcessingMessage, isLoadingHistory,
    isContextPanelOpen, isAgentSelectionRequired, currentThreadTitle, isLoadingTitle, chatInputText,
    selectedAgentDetails, totalTokens, totalCost, selectedModelName, addMessage, clearMessages,
    startProcessing, stopProcessing, toggleContextPanel, startNewConversation, updateThreadTitle,
  ]);

  return (
    <ChatContext.Provider value={contextValue}>
      {children}
    </ChatContext.Provider>
  );
};