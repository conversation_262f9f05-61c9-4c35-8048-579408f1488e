fastapi>=0.100.0,<0.112.0
uvicorn[standard]>=0.20.0,<0.31.0
pydantic>=2.0,<3.0
supabase>=1.0,<3.0 # Official Supabase client (includes async)
python-dotenv>=1.0.0,<2.0.0
python-jose[cryptography]>=3.3.0,<4.0.0 # For JWT verification
httpx>=0.20.0, <0.28.0
pydantic-settings>=2.0,<3.0 # For settings management
asyncpg>=0.28.0,<0.31.0 # For direct PostgreSQL connections with asyncpg pools
aiohttp>=3.8.0,<4.0.0 # For async HTTP requests to external services (N8N)