import React from 'react';
import { format } from 'date-fns';
import { es } from 'date-fns/locale';
import { 
  Eye, 
  CheckCircle, 
  XCircle, 
  Clock, 
  Settings,
  MoreHorizontal 
} from 'lucide-react';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '../UI/table';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '../UI/dropdown-menu';
import { Button } from '../UI/button';
import { Badge } from '../UI/badge';
import { Checkbox } from '../UI/checkbox';
import {
  EvaluacionListItem,
  EstadoEvaluacion
} from '../../types/evaluaciones';

interface EvaluationsTableProps {
  evaluaciones: EvaluacionListItem[];
  selectedIds: number[];
  onToggleSelection: (id: number) => void;
  onSelectAll: () => void;
  onClearSelection: () => void;
  onViewDetails: (id: number) => void;
  onMarkReviewed: (id: number) => void;
  onDiscard: (id: number) => void;
  isLoading?: boolean;
}

const getEstadoBadge = (estado: EstadoEvaluacion) => {
  const variants = {
    [EstadoEvaluacion.PENDIENTE_REVISION]: {
      variant: 'secondary' as const,
      icon: Clock,
      label: 'Pendiente Revisión'
    },
    [EstadoEvaluacion.REVISADO]: {
      variant: 'default' as const,
      icon: CheckCircle,
      label: 'Revisado'
    },
    [EstadoEvaluacion.PENDIENTE_MEJORAS]: {
      variant: 'destructive' as const,
      icon: Settings,
      label: 'Pendiente Mejoras'
    },
    [EstadoEvaluacion.MEJORAS_APLICADAS]: {
      variant: 'default' as const,
      icon: CheckCircle,
      label: 'Mejoras Aplicadas'
    },
  };

  const config = variants[estado];
  const Icon = config.icon;

  return (
    <Badge variant={config.variant} className="flex items-center gap-1">
      <Icon className="h-3 w-3" />
      {config.label}
    </Badge>
  );
};

const getPuntuacionColor = (puntuacion: number) => {
  if (puntuacion >= 8) return 'text-green-600 font-semibold';
  if (puntuacion >= 6) return 'text-yellow-600 font-semibold';
  return 'text-red-600 font-semibold';
};

export const EvaluationsTable: React.FC<EvaluationsTableProps> = ({
  evaluaciones,
  selectedIds,
  onToggleSelection,
  onSelectAll,
  onClearSelection,
  onViewDetails,
  onMarkReviewed,
  onDiscard,
  isLoading = false,
}) => {
  const allSelected = evaluaciones.length > 0 && selectedIds.length === evaluaciones.length;
  const someSelected = selectedIds.length > 0 && selectedIds.length < evaluaciones.length;

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (evaluaciones.length === 0) {
    return (
      <div className="text-center py-12">
        <div className="text-gray-500 text-lg mb-2">No hay evaluaciones disponibles</div>
        <div className="text-gray-400 text-sm">
          Las evaluaciones aparecerán aquí cuando se generen desde los workflows de n8n
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {/* Selection Actions */}
      {selectedIds.length > 0 && (
        <div className="flex items-center gap-2 p-3 bg-blue-50 rounded-lg border border-blue-200">
          <span className="text-sm text-blue-700">
            {selectedIds.length} evaluación{selectedIds.length !== 1 ? 'es' : ''} seleccionada{selectedIds.length !== 1 ? 's' : ''}
          </span>
          <div className="flex gap-2 ml-auto">
            <Button
              size="sm"
              variant="outline"
              onClick={() => {
                selectedIds.forEach(id => onMarkReviewed(id));
                onClearSelection();
              }}
            >
              <CheckCircle className="h-4 w-4 mr-1" />
              Marcar Revisadas
            </Button>
            <Button
              size="sm"
              variant="outline"
              onClick={onClearSelection}
            >
              Cancelar
            </Button>
          </div>
        </div>
      )}

      {/* Table */}
      <div className="border rounded-lg overflow-hidden">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead className="w-12">
                <Checkbox
                  checked={allSelected}
                  ref={(el) => {
                    if (el) el.indeterminate = someSelected;
                  }}
                  onCheckedChange={(checked) => {
                    if (checked) {
                      onSelectAll();
                    } else {
                      onClearSelection();
                    }
                  }}
                />
              </TableHead>
              <TableHead>Agente</TableHead>
              <TableHead>Puntuación</TableHead>
              <TableHead>Estado</TableHead>
              <TableHead>Fecha</TableHead>
              <TableHead>Workflow</TableHead>
              <TableHead className="w-12"></TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {evaluaciones.map((evaluacion) => (
              <TableRow key={evaluacion.id} className="hover:bg-gray-50">
                <TableCell>
                  <Checkbox
                    checked={selectedIds.includes(evaluacion.id)}
                    onCheckedChange={() => onToggleSelection(evaluacion.id)}
                  />
                </TableCell>
                <TableCell>
                  <div className="font-medium">
                    {evaluacion.nombre_agente_amigable}
                  </div>
                  <div className="text-sm text-gray-500">
                    ID: {evaluacion.execution_id.slice(0, 8)}...
                  </div>
                </TableCell>
                <TableCell>
                  <span className={`text-lg ${getPuntuacionColor(evaluacion.puntuacion)}`}>
                    {evaluacion.puntuacion.toFixed(1)}/10
                  </span>
                </TableCell>
                <TableCell>
                  {getEstadoBadge(evaluacion.estado)}
                </TableCell>
                <TableCell>
                  <div className="text-sm">
                    {format(new Date(evaluacion.created_at), 'dd/MM/yyyy', { locale: es })}
                  </div>
                  <div className="text-xs text-gray-500">
                    {format(new Date(evaluacion.created_at), 'HH:mm', { locale: es })}
                  </div>
                </TableCell>
                <TableCell>
                  <div className="text-sm font-mono text-gray-600">
                    {evaluacion.n8n_workflow_id}
                  </div>
                </TableCell>
                <TableCell>
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" size="sm">
                        <MoreHorizontal className="h-4 w-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuItem onClick={() => onViewDetails(evaluacion.id)}>
                        <Eye className="h-4 w-4 mr-2" />
                        Ver Detalles
                      </DropdownMenuItem>
                      {evaluacion.estado === EstadoEvaluacion.PENDIENTE_REVISION && (
                        <DropdownMenuItem onClick={() => onMarkReviewed(evaluacion.id)}>
                          <CheckCircle className="h-4 w-4 mr-2" />
                          Marcar Revisada
                        </DropdownMenuItem>
                      )}
                      <DropdownMenuItem 
                        onClick={() => onDiscard(evaluacion.id)}
                        className="text-red-600"
                      >
                        <XCircle className="h-4 w-4 mr-2" />
                        Descartar
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>
    </div>
  );
};
