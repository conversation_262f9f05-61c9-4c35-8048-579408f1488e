from pydantic import BaseModel, <PERSON>
from typing import Optional
from uuid import UUID
from datetime import datetime
from decimal import Decimal # Use Decimal for numeric type

class ChatMessageBase(BaseModel):
    """Base model for chat messages/events."""
    thread_id: int
    content: Optional[str] = None
    type: Optional[str] = None # e.g., "user", "agent", "answer", "observation", "tool_use", "tool_output"
    from_sender: Optional[str] = Field(None, alias="from") # "User" or "Agent"
    message_id: Optional[Decimal] = None # Using Decimal for numeric SQL type
    agent_id: Optional[UUID] = None
    user_id: Optional[UUID] = None # Assuming this links to the user who initiated/participated
    tool_name: Optional[str] = None # Added field for actual tool name
    input_token_cost: Optional[Decimal] = None # Cost for input tokens
    output_token_cost: Optional[Decimal] = None # Cost for output tokens

class ChatMessageCreate(BaseModel):
    """Model for creating a new chat message entry (e.g., user sending a message)."""
    thread_id: int
    content: str # User message content is required
    agent_id: UUID # Agent must be specified when user sends message
    user_id: UUID # User must be specified
    model_name: Optional[str] = None # Added for LLM model selection
    # message_id will likely be generated or handled by the n8n flow/DB trigger
    # type and from_sender will be set based on context (e.g., type="user", from_sender="User")

class ChatMessageInDBBase(ChatMessageBase):
    """Base model for chat messages stored in the database."""
    id: UUID # Add the primary key field from the 'threads' table
    created_at: datetime
 
    class Config:
        from_attributes = True # Pydantic V2 alias for orm_mode
        populate_by_name = True # Allows using 'from' field name

class ChatMessage(ChatMessageInDBBase):
    """Model representing a chat message as returned from the API."""
    pass

# Potentially add models for specific webhook payloads if needed
class WebhookPayload(BaseModel):
    thread_id: int
    agent_id: UUID
    message: str
    user_id: UUID # Include user_id in webhook payload
    user_message_row_id: UUID # The primary key of the user's message row in the 'threads' table
    model_name: Optional[str] = None # Added for LLM model selection

class TranscriptionRequest(BaseModel):
    # Define based on how audio data will be sent from frontend
    # Option 1: Base64 encoded string
    audio_data_base64: Optional[str] = None
    # Option 2: Or expect a file upload directly in the endpoint
    # file: UploadFile = File(...) # If using FastAPI's UploadFile

class TranscriptionResponse(BaseModel):
    text: str

# Models for thread metadata
class ThreadMetadataBase(BaseModel):
    """Base model for thread metadata."""
    thread_id: int
    titulo: Optional[str]

class ThreadMetadataCreate(BaseModel):
    """Model for creating thread metadata."""
    thread_id: int
    titulo: str = "Chat sin título"

class ThreadMetadataUpdate(BaseModel):
    """Model for updating thread metadata."""
    titulo: str = Field(..., min_length=1, max_length=200, description="Title must not be empty and max 200 characters")

class ThreadMetadata(ThreadMetadataBase):
    """Model representing thread metadata as returned from the API."""
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True

# Model for representing a thread in the history list
class ThreadSummary(BaseModel):
    thread_id: int
    last_updated: datetime # Timestamp of the last message/activity in the thread
    preview: Optional[str] = None # Optional preview of the last message content
    titulo: Optional[str] = None # Thread title from metadata