from pydantic import BaseModel
from typing import Optional
from uuid import UUID
from datetime import datetime

class LLMModel(BaseModel):
    """Base model for LLM model data."""
    id: UUID
    provider_id: Optional[UUID] = None
    model_name: str
    display_name: Optional[str] = None
    description: Optional[str] = None
    context_window: Optional[int] = None
    active: bool
    created_at: datetime

    class Config:
        from_attributes = True