import { useState, useEffect, useCallback } from 'react';

// Import ChatMessage type for compatibility
import { ChatMessage } from '../types/chat';
import { API_BASE_URL } from '../config/api';

// Define a basic Message type for the virtual scroll that extends ChatMessage
interface Message extends ChatMessage {
  [key: string]: unknown; // Allow additional properties
}

interface VirtualScrollOptions {
  itemHeight: number;
  containerHeight: number;
  overscan?: number; // Number of items to render outside visible area
  totalItems: number;
}

interface VirtualScrollResult {
  startIndex: number;
  endIndex: number;
  visibleItems: number;
  scrollTop: number;
  totalHeight: number;
  offsetY: number;
}

export const useVirtualScroll = ({
  itemHeight,
  containerHeight,
  overscan = 5,
  totalItems,
}: VirtualScrollOptions): [VirtualScrollResult, (scrollTop: number) => void] => {
  const [scrollTop, setScrollTop] = useState(0);

  // Calculate visible range
  const startIndex = Math.max(0, Math.floor(scrollTop / itemHeight) - overscan);
  const visibleItems = Math.ceil(containerHeight / itemHeight);
  const endIndex = Math.min(totalItems - 1, startIndex + visibleItems + overscan * 2);

  // Calculate total height and offset
  const totalHeight = totalItems * itemHeight;
  const offsetY = startIndex * itemHeight;

  const handleScroll = useCallback((newScrollTop: number) => {
    setScrollTop(newScrollTop);
  }, []);

  return [
    {
      startIndex,
      endIndex,
      visibleItems,
      scrollTop,
      totalHeight,
      offsetY,
    },
    handleScroll,
  ];
};

// Hook for managing paginated data loading
interface UsePaginatedMessagesOptions {
  threadId: number | null;
  pageSize?: number;
  initialLoad?: boolean;
}

interface PaginatedMessagesResult {
  messages: Message[];
  totalCount: number;
  isLoading: boolean;
  hasMore: boolean;
  loadMore: () => Promise<void>;
  loadInitial: () => Promise<void>;
  reset: () => void;
}

export const usePaginatedMessages = ({
  threadId,
  pageSize = 50,
  initialLoad = true,
}: UsePaginatedMessagesOptions): PaginatedMessagesResult => {
  const [messages, setMessages] = useState<Message[]>([]);
  const [totalCount, setTotalCount] = useState(0);
  const [isLoading, setIsLoading] = useState(false);
  const [currentOffset, setCurrentOffset] = useState(0);
  const [hasMore, setHasMore] = useState(true);

  const reset = useCallback(() => {
    setMessages([]);
    setTotalCount(0);
    setCurrentOffset(0);
    setHasMore(true);
  }, []);

  const loadMessages = useCallback(async (offset: number, isInitial: boolean = false) => {
    if (!threadId || isLoading) return;

    setIsLoading(true);
    try {
      // Get access token
      const session = JSON.parse(localStorage.getItem('sb-session') || '{}');
      const accessToken = session?.access_token;

      if (!accessToken) {
        console.error('No access token found');
        return;
      }

      // Fetch messages with pagination
      const messagesUrl = `${API_BASE_URL}/threads/${threadId}`;
      const params = new URLSearchParams({
        limit: pageSize.toString(),
        offset: offset.toString(),
        load_recent: 'true',
      });

      const response = await fetch(`${messagesUrl}?${params}`, {
        headers: {
          'Authorization': `Bearer ${accessToken}`,
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error(`Failed to fetch messages: ${response.statusText}`);
      }

      const newMessages = await response.json();

      // Get total count if this is the first load
      if (isInitial || totalCount === 0) {
        const countUrl = `${API_BASE_URL}/threads/${threadId}/count`;
        const countResponse = await fetch(countUrl, {
          headers: {
            'Authorization': `Bearer ${accessToken}`,
            'Content-Type': 'application/json',
          },
        });

        if (countResponse.ok) {
          const countData = await countResponse.json();
          setTotalCount(countData.message_count);
        }
      }

      if (isInitial) {
        setMessages(newMessages);
        setCurrentOffset(newMessages.length);
      } else {
        setMessages(prev => [...prev, ...newMessages]);
        setCurrentOffset(prev => prev + newMessages.length);
      }

      setHasMore(newMessages.length === pageSize && currentOffset + newMessages.length < totalCount);

    } catch (error) {
      console.error('Error loading messages:', error);
    } finally {
      setIsLoading(false);
    }
  }, [threadId, pageSize, isLoading, totalCount, currentOffset]);

  const loadMore = useCallback(() => {
    return loadMessages(currentOffset, false);
  }, [loadMessages, currentOffset]);

  const loadInitial = useCallback(() => {
    reset();
    return loadMessages(0, true);
  }, [loadMessages, reset]);

  // Load initial messages when threadId changes
  useEffect(() => {
    if (threadId && initialLoad) {
      loadInitial();
    }
  }, [threadId, initialLoad, loadInitial]);

  return {
    messages,
    totalCount,
    isLoading,
    hasMore,
    loadMore,
    loadInitial,
    reset,
  };
};
