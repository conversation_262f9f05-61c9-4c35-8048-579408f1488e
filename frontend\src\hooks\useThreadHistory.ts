import { useQuery } from '@tanstack/react-query';
import { ChatMessage } from '../contexts/ChatContext';
import { useAuth } from './useAuth';
import { API_BASE_URL } from '../config/api';

// Define a more precise raw type from the API to avoid 'any' and fix mapping issues.
// This is based on the backend's ChatMessage model.
interface RawHistoryMessage {
  id?: string | number;
  thread_id: number | string | bigint;
  content: string | null;
  type: string | null;
  from: 'User' | 'Agent' | null; // The key difference from ChatMessage
  message_id: number | null;
  agent_id: string | null;
  user_id: string | null;
  created_at: string;
  tool_name?: string | null;
  input_token_cost?: number | null;
  output_token_cost?: number | null;
}

// The fetcher function
const fetchThreadHistory = async (threadId: number | string, accessToken: string): Promise<ChatMessage[]> => {
  const historyUrl = `${API_BASE_URL}/threads/${threadId}`;
  const response = await fetch(historyUrl, {
    headers: {
      'Authorization': `Bearer ${accessToken}`,
    },
  });

  if (!response.ok) {
    const errorData = await response.json().catch(() => ({ detail: `HTTP error ${response.status}` }));
    throw new Error(`Failed to fetch history: ${errorData.detail || response.statusText}`);
  }

  const rawFetchedMessages: RawHistoryMessage[] = await response.json();

  // Manually map 'from' to 'from_sender' and ensure id is a string
  const mappedMessages: ChatMessage[] = rawFetchedMessages.map(msg => ({
    ...msg,
    id: msg.id?.toString(),
    from_sender: msg.from,
    input_token_cost: msg.input_token_cost,
    output_token_cost: msg.output_token_cost,
  }));

  // IMPORTANTE: El backend devuelve mensajes en orden DESC (más recientes primero)
  // Necesitamos invertir el orden para mostrar cronológicamente (más antiguos primero)
  return mappedMessages.reverse();
};

export const useThreadHistory = (threadId: number | null) => {
  const { session } = useAuth();
  const accessToken = session?.access_token;

  return useQuery<ChatMessage[], Error>({
    queryKey: ['threadHistory', threadId, accessToken],
    queryFn: () => {
      if (!threadId || !accessToken) {
        return Promise.resolve([]);
      }
      return fetchThreadHistory(threadId, accessToken);
    },
    enabled: !!threadId && !!accessToken, // Only run the query if threadId and accessToken are available
    staleTime: 1000 * 60 * 5, // 5 minutes
    refetchOnWindowFocus: true, // Keep it true, TanStack Query handles this safely
  });
};