from uuid import uuid4, <PERSON>UID # Ensured UUID is imported
import datetime

from app.models.crm_persona import Person<PERSON><PERSON><PERSON>, Persona
from app.core.database import get_supabase_client # Or your DB session manager
from pydantic import ValidationError
# Removed redundant from uuid import UUID

async def create_persona(persona_in: PersonaCreate, user_id: UUID) -> dict: # Removed db: Session
    """
    Create a new persona.
    `user_id` is the ID of the user performing the action, for audit purposes if needed.
    """
    supabase = await get_supabase_client() # Fixed: Added await since client is async
    
    persona_data = persona_in.model_dump(exclude_none=True)
    persona_data['id'] = str(uuid4())
    persona_data['fecha_alta'] = datetime.datetime.now(datetime.timezone.utc).isoformat()
    # `activo` is already handled by Pydantic model default
    # `user_id` is not directly on 'personas' table for 'creado_por'

    try:
        response = await supabase.table('personas').insert(persona_data).execute()
        
        if response.data and len(response.data) > 0:
            return response.data[0]
        else:
            # Log full response for debugging
            print(f"Supabase insert response for persona: {response}") 
            raise Exception("Failed to create persona or retrieve created data.")

    except Exception as e:
        print(f"Error creating persona: {e}")
        raise e

# Placeholder for other service functions (get, update, delete)

async def get_personas(search: str | None = None, empresa_id: UUID | None = None, skip: int = 0, limit: int = 100) -> list[dict]:
    """
    Retrieve personas, with optional search and filtering by empresa_id.

    Args:
        search: Optional search string to filter by name, apellidos, or email
        empresa_id: Optional UUID to filter by empresa
        skip: Number of records to skip (must be >= 0)
        limit: Maximum number of records to return (must be > 0 and <= 1000)
    """
    # Input validation
    if skip < 0:
        raise ValueError("skip must be >= 0")
    if limit <= 0 or limit > 1000:
        raise ValueError("limit must be > 0 and <= 1000")

    supabase = await get_supabase_client() # Added await
    # Base query: select all columns, order by nombre then apellidos
    # The frontend expects 'empresa' to be an object with 'nombre' for subLabel.
    # So we need to fetch related empresa data.
    # Adjust the select string for Supabase to fetch related data: `*, empresa:empresas(nombre)`
    # This assumes 'empresa_id' in 'personas' table is a foreign key to 'id' in 'empresas' table.
    # And that RLS allows this join.
    query = supabase.table('personas').select("*, empresa:empresas (id, nombre)").order('apellidos').order('nombre').offset(skip).limit(limit)

    if empresa_id:
        query = query.eq('empresa_id', str(empresa_id))

    if search:
        # Properly escape search string to prevent SQL injection
        escaped_search = search.replace('%', '\\%').replace('_', '\\_')
        search_term = f"%{escaped_search}%"
        # Searching in nombre, apellidos, or email
        # Note: Supabase `or_` filter might need specific formatting for joined tables if searching in empresa.nombre
        # For now, searching only in persona fields.
        query = query.or_(f"nombre.ilike.{search_term},apellidos.ilike.{search_term},email.ilike.{search_term}")
        
    try:
        response = query.execute() # Removed await
        if response.data:
            # Validate data against the Persona model before returning
            validated_personas = []
            for persona_data in response.data:
                try:
                    validated_personas.append(Persona.model_validate(persona_data))
                except ValidationError as ve:
                    print(f"Pydantic ValidationError for persona_data: {persona_data}")
                    print(f"Details: {ve.errors()}")
                    # Decide if you want to skip this record or raise immediately
                    # For now, let's raise to see the first error clearly
                    raise ve
            # Ensure the 'empresa' field is correctly structured or transformed if needed
            # The select "empresa:empresas(nombre)" should return "empresa": {"nombre": "Empresa Name"}
            # Persona.model_validate should handle the nested EmpresaBasicInfo if data is correct
            return validated_personas
        return []
    except ValidationError as ve_outer: # Catch ValidationError specifically
        # This will catch the re-raised ValidationError from the loop
        print(f"Outer Pydantic ValidationError in get_personas: {ve_outer.errors()}")
        raise ve_outer
    except Exception as e: 
        print(f"Generic error fetching or validating personas: {e}")
        raise e
